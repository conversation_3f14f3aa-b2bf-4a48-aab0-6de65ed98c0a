{"name": "invoice-generator", "version": "0.1.0", "private": true, "homepage": "/pdfInvoiceGenerator", "dependencies": {"@react-pdf/renderer": "^4.3.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "buffer": "^6.0.3", "react": "^19.1.0", "react-datepicker": "^8.3.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.1", "react-scripts": "5.0.1", "text-encoding": "^0.7.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "jest": {"transform": {"^.+\\.(js|jsx|ts|tsx)$": "babel-jest"}, "transformIgnorePatterns": ["node_modules/(?!(react-pdf|@react-pdf)/)"]}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "babel-jest": "^29.7.0"}}