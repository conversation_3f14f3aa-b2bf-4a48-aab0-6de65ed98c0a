# PDF Invoice Generator

This project is a React-based application for generating PDF invoices. It uses `@react-pdf/renderer` to create and style PDF documents dynamically based on user input.

## Features

- Generate professional PDF invoices.
- Customizable invoice details including client information, event details, and payment terms.
- Styled using `@react-pdf/renderer` for consistent PDF formatting.
- **Client Management System**: Store and manage client information with search functionality.
- **Invoice Number Auto-Generation**: Automatic sequential invoice numbering with manual override capability.
- **Client Selection**: Pre-populate client information from existing clients.
- **Duplicate Prevention**: Automatic cleanup of duplicate client entries.
- **Invoice History**: Track all generated invoices with client association.
- **Search & Filter**: Search clients by name, email, or phone number.

## Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   ```
2. Navigate to the project directory:
   ```bash
   cd invoice-generator
   ```
3. Install dependencies:
   ```bash
   npm install
   ```

## Usage

1. Start the development server:
   ```bash
   npm start
   ```
2. Open your browser and navigate to `http://localhost:3000/pdfInvoiceGenerator`.

## Application Structure

### Client Management
- **Client Index Page**: View all clients with search and filtering capabilities
- **Client Search**: Real-time search through existing clients
- **Client Selection**: Click on any client to pre-populate invoice form
- **Duplicate Cleanup**: Automatic merging of duplicate client entries

### Invoice Generation
- **Auto Invoice Numbers**: Sequential numbering (BM-001, BM-002, etc.)
- **Manual Override**: Edit invoice numbers as needed
- **Client Auto-Population**: Select existing clients to fill form automatically
- **Invoice Tracking**: All invoices are saved with client association

### Data Storage
- **Local Storage**: All data is stored locally in the browser
- **Client Database**: Persistent client information with usage tracking
- **Invoice History**: Complete invoice records with metadata

## Deployment

The application is configured to be deployed at the URL path `/pdfInvoiceGenerator/`. This is set in the `package.json` file with the `homepage` field.

To build the application for production:
```bash
npm run build
```

The built files will be in the `build` directory and can be deployed to any static hosting service like S3, Netlify, or GitHub Pages.

## AWS Upload to release to Production
To upload the changed files to the S3 bucket www.belindamarks.com.au/pdfInvoiceGenerator/ using the AWS CLI, follow these steps:

1. **Build the Production Version**
Ensure you have the latest production build of your application:

This will generate the build folder with the updated files.

2. **Upload Files to S3**
Use the AWS CLI to sync the build folder to the S3 bucket:
```bash
aws s3 sync build/ s3://www.belindamarks.com.au/pdfInvoiceGenerator --acl public-read
```
**Explanation:**
build: The source directory containing the production files.
s3://www.belindamarks.com.au/pdfInvoiceGenerator: The target S3 bucket and subdirectory.

--acl public-read: Makes the files publicly accessible.

3. Verify the Upload
After the upload, verify that the files are accessible:

Open your browser and navigate to:
Ensure the application loads correctly.

## Folder Structure

- `src/components`: Contains React components for the application.
  - `atoms/`: Basic form components (inputs, buttons)
  - `molecules/`: Composite components (form rows, client selector)
  - `organisms/`: Complex components (invoice form)
  - `pages/`: Page-level components (client index)
  - `templates/`: Layout templates
  - `pdf/`: PDF generation components
  - `preview/`: Invoice preview components
- `src/utils`: Utility functions and helpers.
  - `storage.ts`: Client and invoice data management
  - `validation.ts`: Form validation logic
- `src/constants`: Application constants and configuration values.
- `src/types`: TypeScript type definitions and interfaces.
- `src/services`: Contains service modules for API calls and business logic.
- `src/hooks`: Custom React hooks for reusable logic.
- `src/assets`: Static assets such as images, fonts, and styles.
- `src/pages`: Page-level components for routing and layout.

## Data Management

### Client Data Structure
```typescript
interface Client {
  id: string;
  name: string;
  email: string;
  phone: string;
  attention?: string;
  createdAt: string;
  lastUsed: string;
  invoiceCount: number;
}
```

### Invoice Data Structure
```typescript
interface Invoice {
  id: string;
  invoiceNumber: string;
  clientId: string;
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  attention: string;
  invoiceDate: string;
  description: string;
  venue: string;
  eventDate: string;
  subtotal: number;
  gstRate: number;
  paymentTerms: string;
  createdAt: string;
  updatedAt: string;
}
```

## License

This project is licensed under the MIT License.
