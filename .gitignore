# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Node.js dependencies
node_modules/

# Build output
dist/
build/

# Logs
logs/
*.log

# Temporary files
*.tmp
*.swp

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# MacOS specific
.DS_Store

# IDE specific
.idea/
.vscode/
*.iml

# testing
/coverage

# misc
npm-debug.log*
yarn-debug.log*
yarn-error.log*
