.App {
  min-height: 100vh;
  background-color: #f8f9fa;
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: var(--primary-color);
  min-height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 1vmin);
  color: white;
  box-shadow: var(--box-shadow);
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.App-nav {
  display: flex;
  gap: 1rem;
  padding: 0 2rem;
}

.nav-button {
  padding: 0.75rem 1.5rem;
  background-color: transparent;
  color: var(--white);
  border: 2px solid var(--white);
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-decoration: none;
  display: flex;
  align-items: center;
}

.nav-button:hover {
  background-color: var(--white);
  color: var(--primary-color);
  transform: translateY(-2px);
}

.nav-button.active {
  background-color: var(--white);
  color: var(--primary-color);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.App-main {
  flex: 1;
  min-height: calc(100vh - 60px);
}

/* Responsive design */
@media (max-width: 768px) {
  .App-nav {
    flex-direction: column;
    gap: 0.5rem;
    padding: 0 1rem;
  }

  .nav-button {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}
