export type FormElement = HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;

export interface Client {
  id: string;
  name: string;
  email: string;
  phone: string;
  attention?: string;
  createdAt: string;
  lastUsed: string;
  invoiceCount: number;
}

export interface Invoice {
  id: string;
  invoiceNumber: string;
  clientId: string;
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  attention: string;
  invoiceDate: string;
  description: string;
  venue: string;
  eventDate: string;
  subtotal: number;
  gstRate: number;
  paymentTerms: string;
  createdAt: string;
  updatedAt: string;
}

export interface FormData {
  attention: string;
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  invoiceNumber: string;
  invoiceDate: string;
  description: string;
  venue: string;
  eventDate: string;
  subtotal: number;
  gstRate: number;
  paymentTerms: string;
  companyName: string;
  companyAddress: string;
}

export interface ValidationError {
  message: string;
  type: 'required' | 'format' | 'range' | 'error' | 'warning' | 'info';
  solution?: string;
}

export type ValidationErrors = Partial<Record<keyof FormData, ValidationError>>;

export interface ValidationMessage {
  message: string;
  type: 'error' | 'warning' | 'info';
  field: keyof FormData;
  solution?: string;
}

// Predefined validation messages
export const validationMessages: Record<string, ValidationMessage> = {
  invalidEmail: {
    message: 'Invalid email format',
    type: 'error',
    field: 'clientEmail',
    solution: 'Please enter a valid email address (e.g., <EMAIL>)'
  },
  futureEventDate: {
    message: 'Event date cannot be in the past',
    type: 'error',
    field: 'eventDate',
    solution: 'Please select a future date for the event'
  },
  lowAmount: {
    message: 'Amount seems unusually low',
    type: 'warning',
    field: 'subtotal',
    solution: 'Verify if the amount is correct'
  }
};

export type ChangeEventHandler = (e: React.ChangeEvent<FormElement>) => void;
export type BlurEventHandler = (e: React.FocusEvent<FormElement>) => void;
