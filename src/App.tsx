import React, { useState } from 'react';
import './App.css';
import './styles.css';
import InvoiceGenerator from './components/InvoiceGenerator';
import { ClientIndex } from './components/pages/ClientIndex';
import { Client, Invoice } from './types/invoice';
import { StorageService } from './utils/storage';

function App() {
  const [currentView, setCurrentView] = useState<'index' | 'invoice'>('index');
  const [selectedClient, setSelectedClient] = useState<Client | undefined>(undefined);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | undefined>(undefined);

  const handleNavigateToInvoice = () => {
    setCurrentView('invoice');
    setSelectedInvoice(undefined); // Clear any selected invoice
  };

  const handleNavigateToIndex = () => {
    setCurrentView('index');
    setSelectedClient(undefined);
    setSelectedInvoice(undefined);
  };

  const handleClientSelect = (client: Client) => {
    setSelectedClient(client);
    setSelectedInvoice(undefined); // Clear any selected invoice
    setCurrentView('invoice');
  };

  const handleInvoiceSelect = (invoiceId: string) => {
    const invoice = StorageService.getInvoice(invoiceId);
    if (invoice) {
      // Also find and set the client associated with the invoice
      const client = StorageService.getClient(invoice.clientId);
      if (client) {
        setSelectedClient(client);
      }

      setSelectedInvoice(invoice);
      setCurrentView('invoice');
    }
  };

  return (
    <div className="App">
      <header className="App-header">
        <nav className="App-nav">
          <button 
            className={`nav-button ${currentView === 'index' ? 'active' : ''}`}
            onClick={handleNavigateToIndex}
          >
            Client Management
          </button>
          <button 
            className={`nav-button ${currentView === 'invoice' ? 'active' : ''}`}
            onClick={handleNavigateToInvoice}
          >
            Invoice Generator
          </button>
        </nav>
      </header>

      <main className="App-main">
        {currentView === 'index' ? (
          <ClientIndex 
            onClientSelect={handleClientSelect}
            onNavigateToInvoice={handleNavigateToInvoice}
            onInvoiceSelect={handleInvoiceSelect}
          />
        ) : (
          <InvoiceGenerator 
            selectedClient={selectedClient} 
            selectedInvoice={selectedInvoice}
          />
        )}
      </main>
    </div>
  );
}

export default App;