import React, { useState, useCallback, memo } from 'react';

import { FormData } from '../../types/invoice';
import { CSSCustomProperties, InvoicePreviewStyleProps } from '../../types/styles';

interface InvoicePreviewProps {
  style?: React.CSSProperties & Partial<CSSCustomProperties>;
  formData: FormData;
  gst: number;
  total: number;
}

const InvoicePreview: React.FC<InvoicePreviewProps> = ({ style, formData, gst, total }) => {
  const [previewStyle, setPreviewStyle] = useState<InvoicePreviewStyleProps>({
    scale: window.innerWidth <= 768 ? 1 : 0.95, // Scale is 1 for mobile devices, 0.95 otherwise
    width: 'var(--a4-width)',
    minHeight: 'fit-content',
    height: 'auto',
    overflow: 'visible',
  });

  return (
    <div
      className="invoice-preview"
      style={{
        ...style,
        ...previewStyle,
        transform: `scale(${previewStyle.scale})`,
        transformOrigin: 'top center',
        display: 'flex',
        flexDirection: 'column',
        height: 'auto',
        minHeight: 'fit-content',
        paddingBottom: '2rem',
        overflow: 'visible',
        '--header-bg-image': `url(require('../assets/images/BelindaMarks-Hero-img.png'))`
      } as React.CSSProperties}
    >
      {/* Header with background image and signature */}
      <div className="invoice-header">
        <div className="invoice-branding">
          <h2>Belinda Marks Entertainment</h2>
          <p>Sydney NSW, Australia</p>
          <p>Email: <EMAIL></p>
          <p>Phone: +61 ***********</p>
        </div>
        <div className="invoice-title-container">
          <h1 className="invoice-title">INVOICE</h1>
          <p className="invoice-number">#{formData.invoiceNumber}</p>
        </div>
      </div>

      {/* Invoice Details Section */}
      <div className="invoice-details-section">
        {/* Client Info */}
        <div className="client-info">
          <div className="client-row">
            <div className="client-label">Client:</div>
            <div className="client-value">{formData.clientName}</div>
          </div>
          {formData.attention && (
            <div className="attention-row">
              <div className="attention-label">Attention:</div>
              <div className="attention-value">{formData.attention}</div>
            </div>
          )}
          <div className="client-row">
            <div className="client-label">Email:</div>
            <div className="client-value">{formData.clientEmail}</div>
          </div>
          {formData.clientPhone && (
            <div className="client-row">
              <div className="client-label">Phone:</div>
              <div className="client-value">{formData.clientPhone}</div>
            </div>
          )}
        </div>

        {/* Invoice Info */}
        <div className="invoice-info">
          <div className="invoice-row">
            <div className="invoice-label">Invoice Date:</div>
            <div className="invoice-value">
              {formData.invoiceDate ? new Date(formData.invoiceDate).toLocaleDateString('en-AU') : ''}
            </div>
          </div>
          <div className="invoice-row">
            <div className="invoice-label">Due Date:</div>
            <div className="invoice-value">
              {formData.invoiceDate && formData.paymentTerms
                ? new Date(new Date(formData.invoiceDate).getTime() + parseInt(formData.paymentTerms) * 24 * 60 * 60 * 1000).toLocaleDateString('en-AU')
                : ''}
            </div>
          </div>
          <div className="invoice-row">
            <div className="invoice-label">Payment Terms:</div>
            <div className="invoice-value">{formData.paymentTerms} days</div>
          </div>
        </div>
      </div>

      {/* Event Details Section */}
      <div className="event-details-section">
        <h3>Event Details</h3>
        <div className="event-details-container">

          {/* Left column - Venue and Date (1/3 width) */}
          <div className="event-details-left">
            {formData.venue && (
              <div className="venue-row">
                <div className="venue-label">Venue:</div>
                <div className="venue-value">{formData.venue}</div>
              </div>
            )}
            {formData.eventDate && (
              <div className="venue-row">
                <div className="venue-label">Event Date:</div>
                <div className="venue-value">
                  {formData.eventDate ? new Date(formData.eventDate).toLocaleDateString('en-AU') : ''}
                </div>
              </div>
            )}
          </div>

          {/* Right column - Description (2/3 width) */}
          <div className="event-details-right">
            <div className="description-content">
              {formData.description && formData.description.split('\n').map((line, index) => (
                <p key={index}>{line}</p>
              ))}
            </div>
          </div>

        </div>
      </div>

      {/* Totals Section */}
      <div className="invoice-totals">
        <div className="total-row">
          <span>Subtotal:</span>
          <span>${typeof formData.subtotal === 'number' ? formData.subtotal.toFixed(2) : '0.00'}</span>
        </div>
        <div className="total-row">
          <span>GST ({formData.gstRate}%):</span>
          <span>${gst.toFixed(2)}</span>
        </div>
        <div className="grand-total">
          <span>Total Due:</span>
          <span>${typeof total === 'number' ? total.toFixed(2) : '0.00'}</span>
        </div>
      </div>

      {/* Payment Information */}
      <div className="payment-info">
        <h3>Payment Information</h3>
        <p>Please make payment by the due date to the following account:</p>
        <div className="payment-details">
          <div className="payment-column">
            <p><strong>Bank:</strong> Westpac</p>
            <p><strong>Account Name:</strong> Belinda & Mark Wybrow</p>
            <p><strong>BSB:</strong> 732-007 <br/><strong>Account Number:</strong> 518-711</p>
            <p><strong>Reference:</strong> Invoice #{formData.invoiceNumber}</p>
            <p><strong>TAX Invoice ABN:</strong> ***********</p>
          </div>
          <div className="payment-column">
            <p><strong>Registered Trade Name:</strong><br/>
            Belinda Jo Wybrow Entertainment</p>
            <p style={{ marginBottom: '4px' }}><strong>Trading As:</strong><br/>
            Belinda Marks</p>
          </div>
        </div>
      </div>

      {/* Thank You Message */}
      <div className="thank-you-section">
        <p className="thank-you"><i>Thank you for your business!</i></p>
        <p>* If you have any questions concerning this invoice, please contact Belinda Marks.</p>
      </div>
    </div>
  );
};

// Use memo to prevent unnecessary re-renders
export default memo(InvoicePreview);
