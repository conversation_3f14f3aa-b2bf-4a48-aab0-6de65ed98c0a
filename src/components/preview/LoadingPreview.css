.loading-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  width: 100%;
  background-color: var(--secondary-light);
  border: 1px solid var(--secondary-color);
  border-radius: 4px;
  margin: 2rem 0;
  position: relative;
  overflow: hidden;
}

.loading-preview::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    to right,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
