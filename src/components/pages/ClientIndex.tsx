import React, { useState, useEffect, useMemo } from 'react';
import { Client } from '../../types/invoice';
import { StorageService } from '../../utils/storage';
import './ClientIndex.css';

interface ClientIndexProps {
  onClientSelect?: (client: Client) => void;
  onNavigateToInvoice?: () => void;
  onInvoiceSelect?: (invoiceId: string) => void;
}

export const ClientIndex: React.FC<ClientIndexProps> = ({
  onClientSelect,
  onNavigateToInvoice,
  onInvoiceSelect
}) => {
  const [clients, setClients] = useState<Client[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'lastUsed' | 'invoiceCount'>('lastUsed');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [isLoading, setIsLoading] = useState(true);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [duplicateCounts, setDuplicateCounts] = useState({ clientDuplicates: 0, invoiceDuplicates: 0 });

  useEffect(() => {
    loadClients();
  }, []);

  const loadClients = async () => {
    setIsLoading(true);
    try {
      const allClients = StorageService.getClients();
      setClients(allClients);
      
      // Get duplicate counts
      const counts = StorageService.getDuplicateCounts();
      setDuplicateCounts(counts);
    } catch (error) {
      console.error('Error loading clients:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredAndSortedClients = useMemo(() => {
    let filtered = clients;

    // Filter by search term
    if (searchTerm) {
      const lowerSearchTerm = searchTerm.toLowerCase();
      filtered = clients.filter(client =>
        client.name.toLowerCase().includes(lowerSearchTerm) ||
        client.email.toLowerCase().includes(lowerSearchTerm) ||
        client.phone.includes(searchTerm)
      );
    }

    // Sort clients
    filtered.sort((a, b) => {
      let aValue: string | number | Date;
      let bValue: string | number | Date;

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'lastUsed':
          aValue = new Date(a.lastUsed);
          bValue = new Date(b.lastUsed);
          break;
        case 'invoiceCount':
          aValue = a.invoiceCount;
          bValue = b.invoiceCount;
          break;
        default:
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [clients, searchTerm, sortBy, sortOrder]);

  const handleClientSelect = (client: Client) => {
    setSelectedClient(client);
    if (onClientSelect) {
      onClientSelect(client);
    }
  };

  const handleInvoiceSelect = (invoiceId: string, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent client selection
    if (onInvoiceSelect) {
      onInvoiceSelect(invoiceId);
    }
  };

  const handleCreateInvoice = () => {
    if (onNavigateToInvoice) {
      onNavigateToInvoice();
    }
  };

  const handleCleanupDuplicates = () => {
    const result = StorageService.cleanupAllDuplicates();
    loadClients(); // Reload the clients list and duplicate counts
    
    // Show feedback to user
    const totalRemoved = result.clients.removed + result.invoices.removed;
    if (totalRemoved > 0) {
      alert(`Cleanup completed successfully!\n\nClients:\n- ${result.clients.removed} duplicate clients removed\n- ${result.clients.merged} clients merged\n\nInvoices:\n- ${result.invoices.removed} duplicate invoices removed\n- ${result.invoices.kept} invoices kept`);
    } else {
      alert('No duplicates found. Your data is already clean!');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-AU', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getClientInvoices = (clientId: string) => {
    return StorageService.getInvoicesByClient(clientId);
  };

  if (isLoading) {
    return (
      <div className="client-index">
        <div className="client-index-loading">
          <div className="loading-spinner"></div>
          <p>Loading clients...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="client-index">
      <div className="client-index-header">
        <h1>Client Management</h1>
        <div className="client-index-actions">
          <button
            className="btn btn-primary"
            onClick={handleCreateInvoice}
          >
            Create New Invoice
          </button>
          <button
            className="btn btn-secondary"
            onClick={handleCleanupDuplicates}
            title={`Remove duplicate clients and invoices. Found: ${duplicateCounts.clientDuplicates} client duplicates, ${duplicateCounts.invoiceDuplicates} invoice duplicates`}
          >
            Cleanup Duplicates
            {(duplicateCounts.clientDuplicates > 0 || duplicateCounts.invoiceDuplicates > 0) && (
              <span className="duplicate-badge">
                {duplicateCounts.clientDuplicates + duplicateCounts.invoiceDuplicates}
              </span>
            )}
          </button>
        </div>
      </div>

      <div className="client-index-controls">
        <div className="search-container">
          <input
            type="text"
            placeholder="Search clients by name, email, or phone..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="sort-controls">
          <label htmlFor="sort-select">Sort by:</label>
          <select
            id="sort-select"
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'name' | 'lastUsed' | 'invoiceCount')}
            className="sort-select"
          >
            <option value="name">Name</option>
            <option value="lastUsed">Last Used</option>
            <option value="invoiceCount">Invoice Count</option>
          </select>

          <button
            className="sort-order-btn"
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            title={`Sort ${sortOrder === 'asc' ? 'descending' : 'ascending'}`}
          >
            {sortOrder === 'asc' ? '↑' : '↓'}
          </button>
        </div>
      </div>

      <div className="client-index-stats">
        <div className="stat-card">
          <span className="stat-number">{clients.length}</span>
          <span className="stat-label">Total Clients</span>
        </div>
        <div className="stat-card">
          <span className="stat-number">
            {clients.reduce((sum, client) => sum + client.invoiceCount, 0)}
          </span>
          <span className="stat-label">Total Invoices</span>
        </div>
        <div className="stat-card">
          <span className="stat-number">
            {clients.filter(client => {
              const lastUsed = new Date(client.lastUsed);
              const thirtyDaysAgo = new Date();
              thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
              return lastUsed > thirtyDaysAgo;
            }).length}
          </span>
          <span className="stat-label">Active (30 days)</span>
        </div>
      </div>

      <div className="client-list">
        {filteredAndSortedClients.length === 0 ? (
          <div className="no-clients">
            {searchTerm ? (
              <>
                <h3>No clients found</h3>
                <p>No clients match your search criteria.</p>
                <button
                  className="btn btn-secondary"
                  onClick={() => setSearchTerm('')}
                >
                  Clear Search
                </button>
              </>
            ) : (
              <>
                <h3>No clients yet</h3>
                <p>Start by creating your first invoice to add clients.</p>
                <button
                  className="btn btn-primary"
                  onClick={handleCreateInvoice}
                >
                  Create First Invoice
                </button>
              </>
            )}
          </div>
        ) : (
          filteredAndSortedClients.map((client) => {
            const clientInvoices = getClientInvoices(client.id);
            const totalRevenue = clientInvoices.reduce((sum, invoice) => sum + invoice.subtotal, 0);

            return (
              <div
                key={client.id}
                className={`client-card ${selectedClient?.id === client.id ? 'selected' : ''}`}
                onClick={() => handleClientSelect(client)}
              >
                <div className="client-card-header">
                  <h3 className="client-name">{client.name}</h3>
                  <div className="client-actions">
                    <button
                      className="btn btn-sm btn-primary"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleClientSelect(client);
                        if (onNavigateToInvoice) onNavigateToInvoice();
                      }}
                    >
                      Create Invoice
                    </button>
                  </div>
                </div>

                <div className="client-card-details">
                  <div className="client-contact">
                    <div className="contact-item">
                      <span className="contact-label">Email:</span>
                      <span className="contact-value">{client.email}</span>
                    </div>
                    <div className="contact-item">
                      <span className="contact-label">Phone:</span>
                      <span className="contact-value">{client.phone}</span>
                    </div>
                    {client.attention && (
                      <div className="contact-item">
                        <span className="contact-label">Attention:</span>
                        <span className="contact-value">{client.attention}</span>
                      </div>
                    )}
                  </div>

                  <div className="client-stats">
                    <div className="stat-item">
                      <span className="stat-value">{client.invoiceCount}</span>
                      <span className="stat-label">Invoices</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-value">${totalRevenue.toLocaleString()}</span>
                      <span className="stat-label">Total Revenue</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-value">{formatDate(client.lastUsed)}</span>
                      <span className="stat-label">Last Used</span>
                    </div>
                  </div>
                </div>

                {clientInvoices.length > 0 && (
                  <div className="client-recent-invoices">
                    <h4>Recent Invoices</h4>
                    <div className="invoice-list">
                      {clientInvoices
                        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
                        .slice(0, 3)
                        .map((invoice) => (
                          <div 
                            key={invoice.id} 
                            className="invoice-item"
                            onClick={(e) => handleInvoiceSelect(invoice.id, e)}
                            title="Click to load this invoice"
                          >
                            <span className="invoice-number">{invoice.invoiceNumber}</span>
                            <span className="invoice-date">{formatDate(invoice.invoiceDate)}</span>
                            <span className="invoice-amount">${invoice.subtotal.toLocaleString()}</span>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </div>
            );
          })
        )}
      </div>
    </div>
  );
}; 