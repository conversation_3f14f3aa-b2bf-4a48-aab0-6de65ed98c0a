.client-index {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background-color: var(--secondary-light);
  min-height: 100vh;
}

.client-index-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--primary-color);
}

.client-index-header h1 {
  margin: 0;
  color: var(--primary-dark);
  font-size: 2rem;
  font-weight: 600;
}

.client-index-actions {
  display: flex;
  gap: 1rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
  background-color: var(--secondary-dark);
  color: var(--white);
}

.btn-secondary:hover {
  background-color: var(--text-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
}

.client-index-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
}

.search-container {
  flex: 1;
  max-width: 400px;
}

.search-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--secondary-color);
  border-radius: var(--border-radius);
  font-size: 0.95rem;
  transition: var(--transition);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(106, 13, 173, 0.2);
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: nowrap;
  white-space: nowrap;
}

.sort-controls label {
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--text-dark);
  margin: 0;
  white-space: nowrap;
}

.sort-select {
  padding: 0.75rem;
  border: 1px solid var(--secondary-color);
  border-radius: var(--border-radius);
  font-size: 0.95rem;
  background-color: var(--white);
  min-width: 120px;
  box-sizing: border-box;
}

.sort-order-btn {
  padding: 0.75rem;
  border: 1px solid var(--secondary-color);
  border-radius: var(--border-radius);
  background-color: var(--white);
  cursor: pointer;
  font-size: 0.95rem;
  transition: var(--transition);
  min-width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  color: #000;
  margin-bottom: 1rem;
}

.sort-order-btn:hover {
  border-color: var(--primary-color);
  background-color: var(--secondary-light);
}

.client-index-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--white);
  padding: 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  text-align: center;
  border: 1px solid var(--secondary-color);
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.95rem;
  color: var(--text-light);
  font-weight: 500;
}

.client-list {
  display: grid;
  gap: 1.5rem;
}

.client-card {
  background: var(--white);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
  border: 1px solid var(--secondary-color);
  transition: var(--transition);
  cursor: pointer;
}

.client-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.client-card.selected {
  border-color: var(--primary-color);
  background-color: rgba(106, 13, 173, 0.05);
}

.client-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.client-name {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-dark);
}

.client-card-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.client-contact {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.contact-item {
  display: flex;
  gap: 0.5rem;
}

.contact-label {
  font-weight: 500;
  color: var(--text-light);
  min-width: 80px;
}

.contact-value {
  color: var(--text-dark);
}

.client-stats {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background-color: var(--secondary-light);
  border-radius: var(--border-radius);
}

.stat-value {
  font-weight: 600;
  color: var(--text-dark);
}

.stat-label {
  font-size: 0.8rem;
  color: var(--text-light);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.client-recent-invoices {
  border-top: 1px solid var(--secondary-color);
  padding-top: 1rem;
}

.client-recent-invoices h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  color: var(--text-dark);
}

.invoice-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.invoice-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin-bottom: 4px;
  background: #f9f9f9;
  cursor: pointer;
  transition: all 0.2s ease;
}

.invoice-item:hover {
  background: #f0f0f0;
  border-color: #d0d0d0;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.invoice-item:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.invoice-number {
  font-weight: 600;
  color: var(--primary-color);
}

.invoice-date {
  color: var(--text-light);
}

.invoice-amount {
  font-weight: 500;
  color: var(--primary-dark);
}

.no-clients {
  text-align: center;
  padding: 3rem 1.5rem;
  background: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.no-clients h3 {
  margin: 0 0 1rem 0;
  color: var(--text-dark);
  font-size: 1.5rem;
}

.no-clients p {
  margin: 0 0 1.5rem 0;
  color: var(--text-light);
  font-size: 1rem;
}

.client-index-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1.5rem;
  background: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--secondary-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .client-index {
    padding: 1rem;
  }

  .client-index-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .client-index-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .search-container {
    max-width: none;
  }

  .client-card-details {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .client-stats {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .stat-item {
    flex: 1;
    min-width: 120px;
  }
}

.duplicate-badge {
  background-color: #ff4444;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  margin-left: 8px;
  line-height: 1;
} 