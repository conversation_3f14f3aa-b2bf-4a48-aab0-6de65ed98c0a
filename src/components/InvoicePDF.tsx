import { Document, Page, Text, View, StyleSheet, Image } from '@react-pdf/renderer';

// Define the type for the data prop
interface InvoicePDFData {
  invoiceNumber: string;
  attention?: string;
  clientName?: string;
  clientEmail?: string;
  clientPhone?: string;
  invoiceDate?: string;
  dueDate?: string;
  description?: string;
  venue?: string;
  eventDate?: string;
  subtotal: number;
  gstRate: number;
  gst: number;
  total: number;
  logo?: string;
  companyName?: string;
  companyAddress?: string;
  companyEmail?: string;
  companyPhone?: string;
  paymentTerms?: string;
}

// Create styles for PDF
const pdfStyles = StyleSheet.create({
  page: {
    padding: 20,
    fontSize: 10,
    lineHeight: 1.6,
    backgroundColor: '#ffffff',
    fontFamily: 'Helvetica',
  },

  header: {
    position: 'relative', // Ensure child elements can be positioned absolutely
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 40,
    borderBottom: '2px solid #6a0dad',
    paddingBottom: 20,
    },
    headerBackground: {
    overflow: 'hidden',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    // Adjust height to cover the top of the page
    opacity: 0.25, // Apply 25% opacity to the background image
    zIndex: -1, // Ensure the background is behind the content
    clipPath: 'polygon(0% 0%, 100% 0%, 100% 120, 0% "30px)', // Clip the bottom 20% of the image
    transform: 'translateY(-50)'
    },
    headerLeft: {
    flexDirection: 'column',
    },
    headerRight: {
    flexDirection: 'column',
    alignItems: 'flex-end',
    },
    invoiceTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#6a0dad',
    marginBottom: 5,
    textTransform: 'uppercase',
    },
    invoiceNumber: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#6a0dad',
    },
    companyName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#6a0dad',
    marginBottom: 5,
    },
    companyDetails: {
    fontSize: 9,
    color: '#000000',
    whiteSpace: 'pre-line', // Preserve line breaks
  },
  section: {
    marginBottom: 20,
  },
  twoColumn: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  columnLeft: {
    flexDirection: 'column',
    width: '33%', // Take up 1/3 of the width
    paddingRight: 10,
    borderRight: '1px dashed #6a0dad',
  },
  columnRight: {
    flexDirection: 'column',
    width: '66%', // Take up 2/3 of the width
    alignItems: 'flex-start', // Left align content
    gap: '2lh', // Add gap of 2 line heights
    paddingLeft: 10,
  },
  column: {
    width: '48%',
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#6a0dad',
    textTransform: 'uppercase',
    borderBottom: '1px solid #cccccc',
    paddingBottom: 3,
  },
  label: {
    fontWeight: 'bold',
    marginRight: 5,
    width: 70, // Fixed width for labels
  },
  value: {
    marginBottom: 5,
  },
  venueValue: {
    marginBottom: 5,
    flexShrink: 1,
    flexWrap: 'wrap',
    maxWidth: 'calc(100% - 75px)', /* Ensures text wraps within the column, accounting for label width */
    wordBreak: 'break-word',
  },
  row: {
    flexDirection: 'row',
    marginBottom: 3,
    flexWrap: 'wrap',
  },
  venueRow: {
    flexDirection: 'row',
    marginBottom: 3,
    flexWrap: 'wrap',
    maxWidth: '100%',
  },
  description: {
    marginTop: 10,
    marginBottom: 10,
    padding: 10,
    backgroundColor: '#e6e6e6',
    borderRadius: 4,
    borderLeft: '4px solid #6a0dad',
  },
  descriptionText: {
    fontSize: 10,
    lineHeight: 1.4,
    textAlign: 'left',
    whiteSpace: 'pre-line', // Preserve line breaks
  },

  totals: {
    marginTop: 20,
    alignItems: 'flex-end',
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '40%',
    marginBottom: 5,
  },
  totalLabel: {
    textAlign: 'right',
    fontWeight: 'bold',
  },
  totalValue: {
    textAlign: 'right',
    width: '30%',
  },
  grandTotal: {
    borderTop: '2px solid #6a0dad',
    paddingTop: 5,
    marginTop: 5,
    fontSize: 12,
    fontWeight: 'bold',
    color: '#6a0dad',
  },

  footer: {
    marginTop: 0,
    padding: 10,
    borderTop: '1px dashed #cccccc',
    fontSize: 9,
    color: '#000000',
    textAlign: 'center',
  },
  logo: {
    width: 80,
    height: 'auto',
    marginBottom: 10,
  },

  paymentInfo: {
    width: '100%',
    marginTop: 20,
    marginBottom: 10,
    borderTop: '2px solid #6a0dad',
    padding: 10,
  },
  paymentRow: {
    width: '100%',
    marginBottom: 10,
  },
  paymentTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#6a0dad',
    marginBottom: 5,
  },
  paymentColumns: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginTop: 10,
  },
  paymentColumn: {
    width: '48%',
  },
  bold: {
    fontWeight: 'bold',
  }
});

// Helper function to safely format dates for PDF
const formatPDFDate = (dateString?: string): string => {
  if (!dateString || dateString === '') return '-';

  // If it's already formatted (contains commas), return as is
  if (typeof dateString === 'string' && dateString.includes(',')) {
    return dateString;
  }

  try {
    // Try to parse as date
    const date = new Date(dateString);

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return '-';
    }

    // Format the date
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch (error) {
    console.error('Error formatting date in PDF:', error);
    return '-';
  }
};

// The PDF component that renders the actual PDF document
function InvoicePDF({ data }: { data: InvoicePDFData }) {
  // Create safe versions of dates
  const invoiceDateFormatted = formatPDFDate(data.invoiceDate);
  const eventDateFormatted = formatPDFDate(data.eventDate);

  // Create safe versions of numeric values
  const subtotal = typeof data.subtotal === 'number' ? data.subtotal : 0;
  const gstRate = typeof data.gstRate === 'number' ? data.gstRate : 0;
  const gst = typeof data.gst === 'number' ? data.gst : 0;
  const total = typeof data.total === 'number' ? data.total : 0;

  // Add default values for optional fields
  const clientName = data.clientName || '-';
  const clientEmail = data.clientEmail || '-';
  const clientPhone = data.clientPhone || '-';
  const venue = data.venue || '-';
  const description = data.description || 'No description provided';

  return (
    <Document>
      <Page size="A4" style={pdfStyles.page}>
        <Image
          style={pdfStyles.headerBackground}
          src={`${process.env.PUBLIC_URL}/images/BelindaMarks-Hero-img.png`} // Using PUBLIC_URL for basepath support
        />
        {/* Header Section */}
        <View id="header" style={pdfStyles.header}>
          <View style={pdfStyles.headerLeft}>
            {data.logo && <Image style={pdfStyles.logo} src={data.logo} />}
            <Text style={pdfStyles.companyName}>Belinda Marks Entertainer</Text>
            <Text style={pdfStyles.companyDetails}>{data.companyAddress || 'Sydney NSW, Australia'}</Text>
            <Text style={pdfStyles.companyDetails}>Email: {data.companyEmail || '<EMAIL>'}</Text>
            <Text style={pdfStyles.companyDetails}>Phone: {data.companyPhone || '+61 ***********'}</Text>
          </View>
          <View style={pdfStyles.headerRight}>
            <Text style={pdfStyles.invoiceTitle}>INVOICE</Text>
            <Text style={pdfStyles.invoiceNumber}>#{data.invoiceNumber}</Text>
          </View>
        </View>

        {/* Client and Invoice Details Section */}
        <View style={{ ...pdfStyles.twoColumn, backgroundColor: '#ffffff', border: '20px solid #ffffff' }}>
          <View style={pdfStyles.column}>
            <Text style={pdfStyles.sectionTitle}>INVOICE DETAILS</Text>
            <View style={pdfStyles.row}>
              <Text style={pdfStyles.label}>Client:</Text>
              <Text style={pdfStyles.value}>{clientName}</Text>
            </View>
            <View style={pdfStyles.row}>
              <Text style={pdfStyles.label}>Email:</Text>
              <Text style={pdfStyles.value}>{clientEmail}</Text>
            </View>
            <View style={pdfStyles.row}>
              <Text style={pdfStyles.label}>Mobile Phone:</Text>
              <Text style={pdfStyles.value}>{clientPhone}</Text>
            </View>
          </View>

          <View style={pdfStyles.column}>
            <Text style={pdfStyles.sectionTitle}> </Text>
            <View style={pdfStyles.row}>
              <Text style={pdfStyles.label}>Invoice Date:</Text>
              <Text style={pdfStyles.value}>{invoiceDateFormatted}</Text>
            </View>

            <View style={pdfStyles.row}>
              <Text style={pdfStyles.label}>Terms:</Text>
              <Text style={pdfStyles.value}>{data.paymentTerms} days</Text>
            </View>
          </View>
        </View>
        <View style={{ ...pdfStyles.sectionTitle, backgroundColor: '#ffffff', borderTop: '0px', borderBottom: '0px', borderLeft: '20px', borderRight: '20px', borderColor: '#ffffff' }}>
        <Text style={{ ...pdfStyles.sectionTitle,border: '0px' }}>EVENT DETAILS</Text>
        </View>
        <View style={{...pdfStyles.twoColumn, backgroundColor: '#ffffff', borderTop: '0px', borderBottom: '0px', borderLeft: '20px', borderRight: '20px', borderColor: '#ffffff' }}>
          <View style={pdfStyles.columnLeft}>
            <View style={pdfStyles.venueRow}>
              <Text style={pdfStyles.label}>Venue:</Text>
              <Text style={pdfStyles.venueValue}>{venue}</Text>
            </View>

             <View style={pdfStyles.venueRow}>
              <Text style={pdfStyles.label}>Event Date:</Text>
              <Text style={pdfStyles.value}>{eventDateFormatted}</Text>
            </View>
          </View>

          <View style={pdfStyles.columnRight}>
            <Text style={pdfStyles.descriptionText}>{description}</Text>
          </View>
        </View>

        {/* Totals Section */}
        <View style={{ ...pdfStyles.totals, backgroundColor: '#ffffff', borderTop: '0px', borderBottom: '0px', borderLeft: '20px', borderRight: '20px', borderColor: '#ffffff' }}>
          <View style={pdfStyles.totalRow}>
            <Text style={pdfStyles.totalLabel}>Subtotal:</Text>
            <Text style={pdfStyles.totalValue}>${subtotal.toFixed(2)}</Text>
          </View>
          <View style={pdfStyles.totalRow}>
            <Text style={pdfStyles.totalLabel}>GST ({gstRate}%):</Text>
            <Text style={pdfStyles.totalValue}>${gst.toFixed(2)}</Text>
          </View>
          <View style={[pdfStyles.totalRow, pdfStyles.grandTotal]}>
            <Text style={pdfStyles.totalLabel}>Total Due:</Text>
            <Text style={pdfStyles.totalValue}>${total.toFixed(2)}</Text>
          </View>
        </View>
 {/* Payment Information */}
        <View style={{ ...pdfStyles.paymentInfo, backgroundColor: '#ffffff', borderTop: '0px', borderBottom: '0px', borderLeft: '20px', borderRight: '20px', borderColor: '#ffffff' }}>
          {/* Row 1 - Title */}
          <View style={[pdfStyles.paymentRow, { marginBottom: 0 }]}>
            <Text style={pdfStyles.paymentTitle}>Payment Information</Text>
          </View>

          {/* Row 2 - Instructions */}
          <View style={pdfStyles.paymentRow}>
            <Text>Please make payment by the due date to the following account:</Text>
          {/* Row 3 - Two Columns */}
          <View style={pdfStyles.paymentColumns}>
            <View style={pdfStyles.paymentColumn}>
              <Text><Text style={pdfStyles.bold}>Bank:</Text> Westpac</Text>
              <Text><Text style={pdfStyles.bold}>Account Name:</Text> Belinda & Mark Wybrow</Text>
              <Text><Text style={pdfStyles.bold}>BSB:</Text><Text> 732-007</Text></Text>
              <Text><Text style={pdfStyles.bold}>Account Number:</Text><Text> 518-711</Text></Text>

              <Text><Text style={pdfStyles.bold}>Reference:</Text> Invoice #{data.invoiceNumber}</Text>
              <Text><Text style={pdfStyles.bold}>TAX Invoice ABN:</Text> ***********</Text>
            </View>
            <View style={pdfStyles.paymentColumn}>
              <Text><Text style={pdfStyles.bold}>Registered Trade Name:</Text></Text>
              <Text>Belinda Jo Wybrow Entertainment</Text>
              <Text><Text style={pdfStyles.bold}>Trading As:</Text></Text>
              <Text>Belinda Marks</Text>
            </View>
            </View>
          </View>

        </View>

        {/* Footer */}
        <View style={{...pdfStyles.footer, backgroundColor: '#ffffff', borderTop: '0px', borderBottom: '0px', borderLeft: '20px', borderRight: '20px', borderColor: '#ffffff'}}>
          <Text style={[pdfStyles.paymentTitle, { fontStyle: 'italic' }]}>Thank you for your business!</Text>
          <Text>* If you have any questions concerning this invoice, please contact Belinda Wybrow.</Text>
        </View>
      </Page>
    </Document>
  );
}

// Export the InvoicePDF component
export { InvoicePDF };

// Create a new file for InvoiceGenerator component
// This component should be moved to its own file
