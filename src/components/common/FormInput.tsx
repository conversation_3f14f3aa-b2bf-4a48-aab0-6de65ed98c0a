import React from 'react';
import { FormElement } from '../../types/invoice';

interface FormInputProps {
  label: string;
  name: string;
  type: string;
  value: string | number;
  onChange: (e: React.ChangeEvent<FormElement>) => void;
  onBlur: (e: React.FocusEvent<FormElement>) => void;
  error?: string;
  touched?: boolean;
}

export const FormInput: React.FC<FormInputProps> = ({
  label,
  name,
  type,
  value,
  onChange,
  onBlur,
  error,
  touched
}) => (
  <div className="form-group">
    <label htmlFor={name}>{label}</label>
    <input
      id={name}
      name={name}
      type={type}
      value={value}
      onChange={onChange}
      onBlur={onBlur}
      className={touched && error ? 'error' : ''}
    />
    {touched && error && <div className="error-message">{error}</div>}
  </div>
);