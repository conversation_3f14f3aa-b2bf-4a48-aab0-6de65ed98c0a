import React, { useState, useMemo, useCallback, useEffect } from 'react';

import { validateField } from '../utils';
import { COMPANY_DETAILS } from '../constants/company';
import { FormData, ValidationErrors, FormElement, Client, Invoice } from '../types/invoice';
import { validateForm } from '../utils/validation';
import { StorageService } from '../utils/storage';
import { ConfirmationDialog } from './atoms';

import { InvoiceGeneratorTemplate } from './templates';

interface InvoiceGeneratorProps {
  onSubmit?: (data: {
    clientName: string;
    invoiceNumber: string;
    eventDetails: string;
    paymentTerms: string;
  }) => void;
  selectedClient?: Client;
  selectedInvoice?: Invoice;
}

export default function InvoiceGenerator({ selectedClient, selectedInvoice }: InvoiceGeneratorProps) {
  // Initial form state with auto-generated invoice number
  const [formData, setFormData] = useState<FormData>({
    attention: '',
    clientName: '',
    clientEmail: '',
    clientPhone: '',
    invoiceNumber: StorageService.generateNextInvoiceNumber(),
    invoiceDate: new Date().toISOString().split('T')[0],
    description: '',
    venue: '',
    eventDate: '',
    subtotal: 0,
    gstRate: 0,
    paymentTerms: '7',
    companyName: COMPANY_DETAILS.name,
    companyAddress: COMPANY_DETAILS.address
  });

  const [errors, setErrors] = useState<ValidationErrors>({});
  const [touched, setTouched] = useState<Record<keyof FormData, boolean>>(
    Object.keys(formData).reduce((acc, key) => ({
      ...acc,
      [key]: false
    }), {} as Record<keyof FormData, boolean>)
  );
  const [validationTriggered, setValidationTriggered] = useState(false);
  const [showDuplicateDialog, setShowDuplicateDialog] = useState(false);
  const [pendingInvoiceNumber, setPendingInvoiceNumber] = useState<string>('');

  // Auto-populate form when a client is selected
  useEffect(() => {
    if (selectedClient) {
      setFormData(prev => ({
        ...prev,
        clientName: selectedClient.name,
        clientEmail: selectedClient.email,
        clientPhone: selectedClient.phone,
        attention: selectedClient.attention || ''
      }));

      // Clear any existing errors for client fields
      setErrors(prev => ({
        ...prev,
        clientName: undefined,
        clientEmail: undefined,
        clientPhone: undefined
      }));

      // Mark client fields as touched since they're now populated
      setTouched(prev => ({
        ...prev,
        clientName: true,
        clientEmail: true,
        clientPhone: true,
        attention: true
      }));
    }
  }, [selectedClient]);

  // Load invoice data when a specific invoice is selected
  useEffect(() => {
    if (selectedInvoice) {
      setFormData(prev => ({
        ...prev,
        attention: selectedInvoice.attention,
        clientName: selectedInvoice.clientName,
        clientEmail: selectedInvoice.clientEmail,
        clientPhone: selectedInvoice.clientPhone,
        invoiceNumber: selectedInvoice.invoiceNumber,
        invoiceDate: selectedInvoice.invoiceDate,
        description: selectedInvoice.description,
        venue: selectedInvoice.venue,
        eventDate: selectedInvoice.eventDate,
        subtotal: selectedInvoice.subtotal,
        gstRate: selectedInvoice.gstRate,
        paymentTerms: selectedInvoice.paymentTerms
      }));

      // Mark all fields as touched since they're populated from existing data
      const allTouched = Object.keys(formData).reduce((acc, key) => ({
        ...acc,
        [key]: true
      }), {} as Record<keyof FormData, boolean>);
      setTouched(allTouched);

      // Clear any existing errors
      setErrors({});
    }
  }, [selectedInvoice]);

  // Reset form when starting a new invoice (no client and no invoice selected)
  useEffect(() => {
    if (!selectedClient && !selectedInvoice) {
      setFormData({
        attention: '',
        clientName: '',
        clientEmail: '',
        clientPhone: '',
        invoiceNumber: StorageService.generateNextInvoiceNumber(),
        invoiceDate: new Date().toISOString().split('T')[0],
        description: '',
        venue: '',
        eventDate: '',
        subtotal: 0,
        gstRate: 0,
        paymentTerms: '7',
        companyName: COMPANY_DETAILS.name,
        companyAddress: COMPANY_DETAILS.address
      });
      setErrors({});
      setTouched({
        attention: false,
        clientName: false,
        clientEmail: false,
        clientPhone: false,
        invoiceNumber: false,
        invoiceDate: false,
        description: false,
        venue: false,
        eventDate: false,
        subtotal: false,
        gstRate: false,
        paymentTerms: false,
        companyName: false,
        companyAddress: false
      });
      setValidationTriggered(false);
    }
  }, [selectedClient, selectedInvoice]);

  const handleChange = useCallback((e: React.ChangeEvent<FormElement>) => {
    const { name, value, type } = e.target;
    const newValue = type === 'number' || name === 'subtotal' ? Number(value) : value;

    setFormData(prev => ({
      ...prev,
      [name]: newValue
    }));

    // Check for duplicate invoice number
    if (name === 'invoiceNumber' && typeof newValue === 'string') {
      const existingInvoice = StorageService.getInvoiceByNumber(newValue);
      if (existingInvoice && (!selectedInvoice || existingInvoice.id !== selectedInvoice.id)) {
        setPendingInvoiceNumber(newValue);
        setShowDuplicateDialog(true);
        return; // Don't update the form data yet
      }
    }

    // Clear errors for the field being changed
    if (errors[name as keyof FormData]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  }, [errors, selectedInvoice]);

  const handleBlur = useCallback((e: React.FocusEvent<FormElement>) => {
    const { name, value } = e.target;
    
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));

    // Only validate on blur if validation has been triggered
    if (validationTriggered) {
      const error = validateField(name as keyof FormData, value);
      if (error) {
        setErrors(prev => ({
          ...prev,
          [name]: error
        }));
      } else {
        setErrors(prev => ({
          ...prev,
          [name]: undefined
        }));
      }
    }
  }, [validationTriggered]);

  const handleFocus = useCallback((e: React.FocusEvent<FormElement>) => {
    const { name } = e.target;

    if (name === 'subtotal') {
      console.log('Subtotal field selected');
      // Additional logic for subtotal field can be added here
    }
  }, []);

  // Handle client selection from ClientSelector
  const handleClientSelect = useCallback((client: Client) => {
    setFormData(prev => ({
      ...prev,
      clientName: client.name,
      clientEmail: client.email,
      clientPhone: client.phone,
      attention: client.attention || ''
    }));

    // Clear any existing errors for client fields
    setErrors(prev => ({
      ...prev,
      clientName: undefined,
      clientEmail: undefined,
      clientPhone: undefined
    }));

    // Mark client fields as touched since they're now populated
    setTouched(prev => ({
      ...prev,
      clientName: true,
      clientEmail: true,
      clientPhone: true,
      attention: true
    }));
  }, []);

  // Trigger validation when PDF button is clicked
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleValidationTrigger = useCallback(() => {
    setValidationTriggered(true);
    
    // Mark all fields as touched to show validation errors
    const allTouched = Object.keys(formData).reduce((acc, key) => ({
      ...acc,
      [key]: true
    }), {} as Record<keyof FormData, boolean>);
    setTouched(allTouched);

    // Run full form validation
    const validationErrors = validateForm(formData);
    setErrors(validationErrors);

    // Return whether the form is valid
    return Object.keys(validationErrors).length === 0;
  }, [formData]);

  // Handle invoice generation and saving
  const handleInvoiceGenerated = useCallback(() => {
    // Save the invoice to storage
    const invoiceData = {
      invoiceNumber: formData.invoiceNumber,
      clientId: '', // We'll need to get this from the client
      clientName: formData.clientName,
      clientEmail: formData.clientEmail,
      clientPhone: formData.clientPhone,
      attention: formData.attention,
      invoiceDate: formData.invoiceDate,
      description: formData.description,
      venue: formData.venue,
      eventDate: formData.eventDate,
      subtotal: formData.subtotal,
      gstRate: formData.gstRate,
      paymentTerms: formData.paymentTerms
    };

    // Find or create client
    const existingClient = StorageService.searchClients(formData.clientName)[0];
    let clientId = '';

    if (existingClient) {
      clientId = existingClient.id;
    } else {
      // Create new client
      const newClient = StorageService.addClient({
        name: formData.clientName,
        email: formData.clientEmail,
        phone: formData.clientPhone,
        attention: formData.attention
      });
      clientId = newClient.id;
    }

    // Save invoice with client ID
    const invoice = StorageService.addInvoice({
      ...invoiceData,
      clientId
    });

    // Save the last invoice number for future auto-generation
    StorageService.saveLastInvoiceNumber(formData.invoiceNumber);

    console.log('Invoice saved:', invoice);
  }, [formData]);

  // Handle duplicate invoice number confirmation
  const handleDuplicateConfirm = useCallback(() => {
    // User confirmed they want to reuse the invoice number
    setFormData(prev => ({
      ...prev,
      invoiceNumber: pendingInvoiceNumber
    }));
    setShowDuplicateDialog(false);
    setPendingInvoiceNumber('');
  }, [pendingInvoiceNumber]);

  const handleDuplicateCancel = useCallback(() => {
    // User cancelled, revert to previous invoice number
    setShowDuplicateDialog(false);
    setPendingInvoiceNumber('');
  }, []);

  const gst = useMemo(() => {
    return (formData.subtotal * formData.gstRate) / 100;
  }, [formData.subtotal, formData.gstRate]);

  const total = useMemo(() => {
    return formData.subtotal + gst;
  }, [formData.subtotal, gst]);

  const formatDate = useCallback((date?: string): string => {
    if (!date) return '';
    return new Date(date).toLocaleDateString('en-AU', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  }, []);

  return (
    <>
      <InvoiceGeneratorTemplate
        formData={formData}
        onChange={handleChange}
        onBlur={handleBlur}
        onFocus={handleFocus}
        errors={errors}
        touched={touched}
        isSubmitting={false}
        gst={gst}
        total={total}
        formatDate={formatDate}
        onClientSelect={handleClientSelect}
        onInvoiceGenerated={handleInvoiceGenerated}
        onValidationTrigger={handleValidationTrigger}
        validationTriggered={validationTriggered}
      />
      
      <ConfirmationDialog
        isOpen={showDuplicateDialog}
        title="Duplicate Invoice Number"
        message={`Invoice number "${pendingInvoiceNumber}" already exists. This could be an intentional edit to fix a mistake. Do you want to reuse this invoice number?`}
        onConfirm={handleDuplicateConfirm}
        onCancel={handleDuplicateCancel}
        confirmText="Reuse Number"
        cancelText="Cancel"
      />
    </>
  );
}
