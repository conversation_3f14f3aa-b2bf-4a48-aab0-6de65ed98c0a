import React, { lazy, Suspense, memo } from 'react';
import { PDFDownloadLink } from '@react-pdf/renderer';
import { InvoicePDF } from '../InvoicePDF';
import { FormData } from '../../types/invoice';

// Lazy load the PDF components
const PDFDownloadButton = lazy(() => 
  import('../molecules/PDFDownloadButton').then(module => ({ 
    default: module.PDFDownloadButton 
  }))
);

interface LazyPDFDownloadProps {
  formData: FormData;
  gst: number;
  total: number;
  fileName: string;
  isSubmitting: boolean;
  hasErrors: boolean;
  isFormComplete: boolean;
  onInvoiceGenerated?: () => void;
  onValidationTrigger?: () => boolean;
  validationTriggered?: boolean;
}

const LazyPDFDownload: React.FC<LazyPDFDownloadProps> = ({
  formData,
  gst,
  total,
  fileName,
  isSubmitting,
  hasErrors,
  isFormComplete,
  onInvoiceGenerated,
  onValidationTrigger,
  validationTriggered
}) => {
  const handleDownload = () => {
    // Call the callback when PDF is generated and downloaded
    if (onInvoiceGenerated) {
      onInvoiceGenerated();
    }
  };

  const handleButtonClick = () => {
    // Trigger validation if not already triggered
    if (!validationTriggered && onValidationTrigger) {
      const isValid = onValidationTrigger();
      // If validation fails, don't proceed with PDF generation
      if (!isValid) {
        return;
      }
    }
  };

  // If validation is triggered and there are errors, show validation button
  if (validationTriggered && hasErrors) {
    return (
      <div className="download-button-container">
        <button
          className="button"
          onClick={handleButtonClick}
          disabled={isSubmitting}
        >
          Validate Form
        </button>
      </div>
    );
  }

  return (
    <div className="download-button-container">
      <Suspense fallback={<button className="button" disabled>Loading PDF Generator...</button>}>
        <PDFDownloadLink
          document={<InvoicePDF data={{ ...formData, gst, total }} />}
          fileName={fileName}
          onClick={handleDownload}
        >
          {({ loading }) => (
            <button
              className="button"
              disabled={loading || isSubmitting}
              onClick={handleButtonClick}
            >
              {loading ? 'Generating PDF...' : 'Download PDF'}
            </button>
          )}
        </PDFDownloadLink>
      </Suspense>
    </div>
  );
};

export default memo(LazyPDFDownload);
