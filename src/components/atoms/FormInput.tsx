import React from 'react';
import { ValidationError, FormData } from '../../types/invoice';

export interface FormInputProps {
  label: string;
  name: keyof FormData;
  value: string | number;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur: (e: React.FocusEvent<HTMLInputElement>) => void;
  onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void;
  error?: ValidationError;
  type?: 'text' | 'number' | 'email' | 'tel' | 'date';
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  touched?: boolean;
  min?: string | number;
  max?: string | number;
  step?: string | number;
  'aria-required'?: string;
  'aria-describedby'?: string;
}

export const FormInput: React.FC<FormInputProps> = ({
  label,
  name,
  value,
  onChange,
  onBlur,
  onFocus,
  error,
  type = 'text',
  placeholder,
  disabled = false,
  required = false,
  touched = false,
}) => {
  const inputId = String(name);

  return (
    <div className="form-group">
      <label htmlFor={inputId} className="form-label">
        {label}
        {required && <span className="required-mark">*</span>}
      </label>
      <input
        id={inputId}
        name={inputId}
        type={type}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        onFocus={onFocus}
        placeholder={placeholder}
        disabled={disabled}
        className={`form-input ${touched && error ? 'form-input-error' : ''}`}
        aria-invalid={touched && error ? 'true' : 'false'}
        aria-describedby={error ? `${inputId}-error` : undefined}
      />
      {touched && error && (
        <div 
          className="form-error" 
          id={`${inputId}-error`} 
          role="alert"
        >
          {error.message}
        </div>
      )}
    </div>
  );
};
