.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--primary-dark);
}

.required-mark {
  color: var(--primary-color);
  margin-left: 0.25rem;
}

.form-input, .form-textarea {
  display: block;
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--secondary-color);
  border-radius: var(--border-radius);
  font-size: 0.95rem;
  transition: var(--transition);
  box-sizing: border-box;
}

.form-input:focus, .form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(106, 13, 173, 0.2);
}

.form-input-error, .form-textarea-error {
  border-color: #e53e3e;
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
  line-height: 1.5;
  font-family: inherit;
}

/* .row-limit {
  font-size: 0.8rem;
  color: var(--text-light);
  font-weight: normal;
} */

.form-error-message {
  color: #e53e3e;
  font-size: 0.85rem;
  margin-top: 0.25rem;
}
