import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { FormInput } from './FormInput';
import { ValidationError, FormData } from '../../types/invoice';

describe('FormInput', () => {
  const mockOnChange = jest.fn();
  const mockOnBlur = jest.fn();

  const defaultProps = {
    label: 'Test Input',
    name: 'clientName' as keyof FormData,
    value: '',
    onChange: mockOnChange,
    onBlur: mockOnBlur,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    const { getByLabelText } = render(<FormInput {...defaultProps} />);
    expect(getByLabelText('Test Input')).toBeInTheDocument();
  });

  it('displays error message when error is present', () => {
    const error: ValidationError = {
      message: 'This is an error',
      type: 'required'
    };

    const { getByText } = render(
      <FormInput
        {...defaultProps}
        error={error}
        touched={true}
      />
    );
    expect(getByText('This is an error')).toBeInTheDocument();
  });

  it('calls onChange handler when input changes', () => {
    const { getByLabelText } = render(<FormInput {...defaultProps} />);
    fireEvent.change(getByLabelText('Test Input'), { target: { value: 'new value' } });
    expect(mockOnChange).toHaveBeenCalled();
  });

  it('calls onBlur handler when input loses focus', () => {
    const { getByLabelText } = render(<FormInput {...defaultProps} />);
    fireEvent.blur(getByLabelText('Test Input'));
    expect(mockOnBlur).toHaveBeenCalled();
  });

  it('applies error class when error is present and touched', () => {
    const error: ValidationError = {
      message: 'This is an error',
      type: 'required'
    };

    const { getByLabelText } = render(
      <FormInput
        {...defaultProps}
        error={error}
        touched={true}
      />
    );
    expect(getByLabelText('Test Input')).toHaveClass('form-input-error');
  });
});
