import React from 'react';

import { ValidationError, FormData } from '../../types/invoice';

export interface FormTextAreaProps {
  label: string;
  name: keyof FormData;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onBlur: (e: React.FocusEvent<HTMLTextAreaElement>) => void;
  error?: ValidationError;
  placeholder?: string;
  rows?: number;
  required?: boolean;
  'aria-describedby'?: string;
}

export const FormTextArea: React.FC<FormTextAreaProps> = ({
  label,
  name,
  value,
  onChange,
  onBlur,
  error,
  placeholder,
  rows = 3,
  required,
  'aria-describedby': ariaDescribedby,
}) => {
  const inputId = String(name);
  
  return (
    <div className="form-group">
      <label htmlFor={inputId} className="form-label">
        {label}
        {required && <span className="required-indicator">*</span>}
      </label>
      <textarea
        id={inputId}
        name={name}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        placeholder={placeholder}
        rows={rows}
        className={`form-textarea ${error ? 'form-textarea-error' : ''}`}
        required={required}
        aria-required={required}
        aria-describedby={ariaDescribedby}
      />
      {error && <div className="error-message">{error.message}</div>}
    </div>
  );
};
