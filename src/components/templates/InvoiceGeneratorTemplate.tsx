import React, { useMemo, lazy, Suspense } from 'react';

import { InvoiceForm } from '../organisms/InvoiceForm';
import LoadingPreview from '../preview/LoadingPreview';
import {
  FormData,
  ValidationError,
  ChangeEvent<PERSON>andler,
  BlurEventHandler,
  Client
} from '../../types/invoice';

import './InvoiceTemplate.css';

// Lazy load components
const LazyPDFDownload = lazy(() => import('../pdf/LazyPDFDownload'));
const InvoicePreview = lazy(() => import('../preview/InvoicePreview'));


interface InvoiceGeneratorTemplateProps {
  formData: FormData;
  onChange: ChangeEventHandler;
  onBlur: BlurEventHandler;
  onFocus?: React.FocusEventHandler<HTMLElement>;
  errors: Partial<Record<keyof FormData, ValidationError>>;
  touched: Record<keyof FormData, boolean>;
  isSubmitting: boolean;
  gst: number;
  total: number;
  formatDate: (date?: string) => string;
  onClientSelect?: (client: Client) => void;
  onInvoiceGenerated?: () => void;
  onValidationTrigger?: () => boolean;
  validationTriggered?: boolean;
}

export const InvoiceGeneratorTemplate: React.FC<InvoiceGeneratorTemplateProps> = ({
  formData,
  onChange,
  onBlur,
  onFocus,
  errors,
  touched,
  isSubmitting,
  gst,
  total,
  formatDate,
  onClientSelect,
  onInvoiceGenerated,
  onValidationTrigger,
  validationTriggered
}) => {
  const hasErrors = useMemo(() => Object.keys(errors).length > 0, [errors]);
  const fileName = useMemo(() => `Inv_${formData.invoiceNumber}.pdf`, [formData.invoiceNumber]);

  // Check if all required fields are filled
  const isFormComplete = useMemo(() => {
    const requiredFields: (keyof FormData)[] = [
      'clientName',
      'clientEmail',
      'description',
      'eventDate',
      'venue',
      'subtotal'
    ];

    return requiredFields.every(field => Boolean(formData[field]));
  }, [formData]);

  // Create error summary for accessibility
  const errorSummary = useMemo(() => {
    if (!hasErrors || !validationTriggered) return null;

    return (
      <div className="validation-summary" role="alert">
        <h4>Please fix the following errors:</h4>
        <ul>
          {Object.entries(errors).map(([field, error]) => (
            error && (
              <li key={field}>
                <strong>{field.replace(/([A-Z])/g, ' $1').toLowerCase()}</strong>: {error.message}
              </li>
            )
          ))}
        </ul>
      </div>
    );
  }, [errors, hasErrors, validationTriggered]);

  return (
    <div className="invoice-container">
      <div className="form-section">
        <InvoiceForm
          formData={formData}
          onChange={onChange}
          errors={errors}
          touched={touched}
          onClientSelect={onClientSelect}
          validationTriggered={validationTriggered}
        />
      </div>

      <div className="preview-section sticky">
        <div className="preview-actions">
          <h2>Invoice Preview</h2>

          {errorSummary}

          <div className="tools-container">
            <div className="tools-left">
              <Suspense fallback={<button className="download-pdf-btn" disabled>Loading...</button>}>
                <LazyPDFDownload
                  formData={formData}
                  gst={gst}
                  total={total}
                  fileName={fileName}
                  isSubmitting={isSubmitting}
                  hasErrors={hasErrors}
                  isFormComplete={isFormComplete}
                  onInvoiceGenerated={onInvoiceGenerated}
                  onValidationTrigger={onValidationTrigger}
                  validationTriggered={validationTriggered}
                />
              </Suspense>
            </div>
          </div>

          <Suspense fallback={<LoadingPreview />}>
            <InvoicePreview
              formData={formData}
              gst={gst}
              total={total}
              style={{
                transform: `scale(0.9)`, // Default scale of 90%
              }}
            />
          </Suspense>
        </div>
      </div>
    </div>
  );
};

export type { InvoiceGeneratorTemplateProps };
