import React, { useCallback } from 'react';
import { FormInput } from '../atoms';
import { FormRow } from './FormRow';
import { FormData, ValidationError, Client } from '../../types/invoice';
import { ClientSelector } from './ClientSelector';

interface ClientInfoSectionProps {
  formData: FormData;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur: (e: React.FocusEvent<HTMLInputElement>) => void;
  errors: Partial<Record<keyof FormData, ValidationError>>;
  touched: Record<keyof FormData, boolean>;
  onClientSelect?: (client: Client) => void;
  validationTriggered?: boolean;
}

/**
 * ClientInfoSection - A molecule component for client information
 *
 * This component follows atomic design principles and represents
 * a composition of atoms (form inputs) arranged in a meaningful section.
 */
export const ClientInfoSection: React.FC<ClientInfoSectionProps> = (props) => {
  const { formData, onChange, onBlur, errors, touched, onClientSelect, validationTriggered } = props;

  // Handle client name input change
  const handleClientNameChange = useCallback((value: string) => {
    const event = {
      target: { name: 'clientName', value }
    } as React.ChangeEvent<HTMLInputElement>;
    onChange(event);
  }, [onChange]);

  // Handle client selection from dropdown
  const handleClientSelect = useCallback((client: Client) => {
    // Auto-populate client information
    const events = [
      { target: { name: 'clientName', value: client.name } },
      { target: { name: 'clientEmail', value: client.email } },
      { target: { name: 'clientPhone', value: client.phone } }
    ] as React.ChangeEvent<HTMLInputElement>[];

    events.forEach(event => onChange(event));

    // Populate attention if available
    if (client.attention) {
      const attentionEvent = {
        target: { name: 'attention', value: client.attention }
      } as React.ChangeEvent<HTMLInputElement>;
      onChange(attentionEvent);
    }

    // Call the parent's onClientSelect if provided
    if (onClientSelect) {
      onClientSelect(client);
    }
  }, [onChange, onClientSelect]);

  return (
    <div className="client-info-section">
      <h3>Client Information</h3>

      <FormInput
        label="Attention of"
        name="attention"
        value={formData.attention}
        onChange={onChange}
        onBlur={onBlur}
        placeholder="Enter attention of name"
        error={touched.attention ? errors.attention : undefined}
      />

      <div className="form-group">
        <label htmlFor="clientName" className="form-label">
          Client Name
          <span className="required-mark">*</span>
        </label>
        <ClientSelector
          value={formData.clientName}
          onChange={handleClientNameChange}
          onSelect={handleClientNameChange}
          onClientSelect={handleClientSelect}
        />
        {touched.clientName && errors.clientName && (
          <div className="form-error" role="alert">
            {errors.clientName.message}
          </div>
        )}
      </div>

      <FormRow id="client-contact-row">
        <FormInput
          label="Email"
          name="clientEmail"
          type="email"
          value={formData.clientEmail}
          onChange={onChange}
          onBlur={onBlur}
          placeholder="<EMAIL>"
          error={touched.clientEmail ? errors.clientEmail : undefined}
        />

        <FormInput
          label="Phone"
          name="clientPhone"
          type="tel"
          value={formData.clientPhone}
          onChange={onChange}
          onBlur={onBlur}
          placeholder="Phone number"
          error={touched.clientPhone ? errors.clientPhone : undefined}
        />
      </FormRow>
    </div>
  );
};
