import React, { memo, useState } from 'react';
import { PDFDownloadLink } from '@react-pdf/renderer';
import { FormData } from '../../types/invoice';
import { InvoicePDF } from '../InvoicePDF';

interface PDFDownloadButtonProps {
  formData: FormData;
  gst: number;
  total: number;
  fileName: string;
  isSubmitting: boolean;
  hasErrors: boolean;
  isFormComplete: boolean;
}

export const PDFDownloadButton: React.FC<PDFDownloadButtonProps> = ({
  formData,
  gst,
  total,
  fileName,
  isSubmitting,
  hasErrors,
  isFormComplete,
}) => {
  const [isPdfGenerated, setIsPdfGenerated] = useState(false);

  const handleGeneratePDF = () => {
    setIsPdfGenerated(true);
  };

  const isDisabled =
    isSubmitting ||
    hasErrors ||
    !isFormComplete;

  if (!isPdfGenerated) {
    return (
      <button
        className="button"
        onClick={handleGeneratePDF}
        disabled={isDisabled}
        type="button"
      >
        {isSubmitting
          ? 'Validating...'
          : !isFormComplete
          ? 'Complete required fields'
          : hasErrors
          ? 'Fix errors to continue'
          : 'Generate PDF'}
      </button>
    );
  }

  return (
    <PDFDownloadLink
      document={<InvoicePDF data={{ ...formData, gst, total }} />}
      fileName={fileName}
      className="download-link"
    >
      {({ loading, error }) => (
        <button
          className="button"
          disabled={loading || !!error}
          aria-busy={loading}
          type="button"
        >
          {loading
            ? 'Generating PDF...'
            : error
            ? 'Error generating PDF'
            : 'Download PDF'}
        </button>
      )}
    </PDFDownloadLink>
  );
};

export default memo(PDFDownloadButton);