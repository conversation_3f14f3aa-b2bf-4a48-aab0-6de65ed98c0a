import React, { useState, useEffect, useRef } from 'react';
import { Client } from '../../types/invoice';
import { StorageService } from '../../utils/storage';
import './ClientSelector.css';

interface ClientSelectorProps {
  value: string;
  onChange: (clientName: string) => void;
  onSelect: (clientName: string) => void;
  onClientSelect?: (client: Client) => void;
}

export const ClientSelector: React.FC<ClientSelectorProps> = ({
  value,
  onChange,
  onSelect,
  onClientSelect
}) => {
  const [searchTerm, setSearchTerm] = useState(value);
  const [filteredClients, setFilteredClients] = useState<Client[]>([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Load clients on component mount
  useEffect(() => {
    if (searchTerm.length > 0) {
      searchClients(searchTerm);
    } else {
      setFilteredClients([]);
    }
  }, [searchTerm]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const searchClients = async (query: string) => {
    setIsLoading(true);
    try {
      const clients = StorageService.searchClients(query);
      setFilteredClients(clients);
      setShowDropdown(clients.length > 0);
    } catch (error) {
      console.error('Error searching clients:', error);
      setFilteredClients([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value;
    setSearchTerm(term);
    onChange(term);
    onSelect(term);
  };

  const handleClientSelect = (client: Client) => {
    setSearchTerm(client.name);
    onChange(client.name);
    onSelect(client.name);
    setShowDropdown(false);
    setFilteredClients([]);
    
    if (onClientSelect) {
      onClientSelect(client);
    }
  };

  const handleInputFocus = () => {
    if (searchTerm.length > 0 && filteredClients.length > 0) {
      setShowDropdown(true);
    }
  };

  const formatLastUsed = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-AU', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  return (
    <div className="client-selector" ref={dropdownRef}>
      <div className="client-selector-input-container">
        <input
          type="text"
          className="client-selector-input"
          value={searchTerm}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          placeholder="Enter client name or search existing clients"
        />
        {isLoading && (
          <div className="client-selector-loading">
            <span>Searching...</span>
          </div>
        )}
      </div>

      {showDropdown && filteredClients.length > 0 && (
        <div className="client-selector-dropdown">
          {filteredClients.map((client) => (
            <div
              key={client.id}
              className="client-selector-option"
              onClick={() => handleClientSelect(client)}
            >
              <div className="client-selector-option-main">
                <div className="client-selector-option-name">{client.name}</div>
                <div className="client-selector-option-email">{client.email}</div>
              </div>
              <div className="client-selector-option-details">
                <span className="client-selector-option-phone">{client.phone}</span>
                <span className="client-selector-option-last-used">
                  Last used: {formatLastUsed(client.lastUsed)}
                </span>
                <span className="client-selector-option-count">
                  {client.invoiceCount} invoice{client.invoiceCount !== 1 ? 's' : ''}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}

      {showDropdown && filteredClients.length === 0 && searchTerm.length > 0 && !isLoading && (
        <div className="client-selector-dropdown">
          <div className="client-selector-no-results">
            No existing clients found. This will create a new client.
          </div>
        </div>
      )}
    </div>
  );
};
