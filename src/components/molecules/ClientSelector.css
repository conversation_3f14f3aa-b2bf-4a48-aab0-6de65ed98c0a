.client-selector {
  position: relative;
  width: 100%;
}

.client-selector-input-container {
  position: relative;
}

.client-selector-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--secondary-color);
  border-radius: var(--border-radius);
  font-size: 0.95rem;
  transition: var(--transition);
  background-color: var(--white);
}

.client-selector-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(106, 13, 173, 0.2);
}

.client-selector-input::placeholder {
  color: var(--text-light);
  font-style: italic;
}

.client-selector-loading {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.9rem;
  color: var(--text-light);
  font-style: italic;
}

.client-selector-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--white);
  border: 1px solid var(--secondary-color);
  border-top: none;
  border-radius: 0 0 var(--border-radius) var(--border-radius);
  box-shadow: var(--box-shadow);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

.client-selector-option {
  padding: 0.75rem;
  cursor: pointer;
  border-bottom: 1px solid var(--secondary-light);
  transition: var(--transition);
}

.client-selector-option:last-child {
  border-bottom: none;
}

.client-selector-option:hover {
  background-color: var(--secondary-light);
}

.client-selector-option-main {
  margin-bottom: 0.5rem;
}

.client-selector-option-name {
  font-weight: 600;
  color: var(--primary-dark);
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.client-selector-option-email {
  color: var(--text-light);
  font-size: 0.9rem;
}

.client-selector-option-details {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: var(--text-light);
}

.client-selector-option-phone {
  background-color: var(--secondary-light);
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius);
}

.client-selector-option-last-used {
  background-color: rgba(106, 13, 173, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius);
}

.client-selector-option-count {
  background-color: rgba(106, 13, 173, 0.2);
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius);
  font-weight: 500;
}

.client-selector-no-results {
  padding: 1rem;
  text-align: center;
  color: var(--text-light);
  font-style: italic;
  background-color: var(--secondary-light);
}

/* Scrollbar styling for dropdown */
.client-selector-dropdown::-webkit-scrollbar {
  width: 6px;
}

.client-selector-dropdown::-webkit-scrollbar-track {
  background: var(--secondary-light);
  border-radius: 3px;
}

.client-selector-dropdown::-webkit-scrollbar-thumb {
  background: var(--secondary-dark);
  border-radius: 3px;
}

.client-selector-dropdown::-webkit-scrollbar-thumb:hover {
  background: var(--text-dark);
}
