import React, { useCallback } from 'react';

import { FormInput } from '../atoms';
import { FormRow } from '../molecules';
import { FormTextArea } from '../atoms';
import { ClientInfoSection } from '../molecules/ClientInfoSection';
import { FormData, ValidationError, FormElement, Client } from '../../types/invoice';
import { StorageService } from '../../utils/storage';
import { validateField } from '../../utils/validation';

interface FormLabelProps {
  htmlFor: string;
  required?: boolean;
  children: React.ReactNode;
}

const FormLabel: React.FC<FormLabelProps> = ({ htmlFor, required, children }) => (
  <label className="form-label" htmlFor={htmlFor}>
    {children}
    {required && <span className="required-indicator" aria-hidden="true">*</span>}
  </label>
);

interface InvoiceFormProps {
  formData: FormData;
  onChange: (e: React.ChangeEvent<FormElement>) => void;
  errors: Partial<Record<keyof FormData, ValidationError>>;
  touched: Record<keyof FormData, boolean>;
  onClientSelect?: (client: Client) => void;
  validationTriggered?: boolean;
}

export const InvoiceForm: React.FC<InvoiceFormProps> = ({
  formData,
  onChange,
  errors,
  touched,
  onClientSelect,
  validationTriggered
}) => {
  const handleBlur = useCallback((e: React.FocusEvent<FormElement>) => {
    const { name, value } = e.target;

    // Only validate on blur if validation has been triggered
    if (validationTriggered) {
      validateField(name as keyof FormData, value);
      // Note: We don't set errors here as they're managed by the parent
    }
  }, [validationTriggered]);

  const handleSubtotalFocus = useCallback((e: React.FocusEvent<HTMLInputElement>) => {
    e.target.select(); // Select all text when the field is focused
  }, []);

  const handleSubtotalBlur = useCallback((e: React.FocusEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    if (!isNaN(value)) {
      onChange({
        ...e,
        target: { ...e.target, value: value.toFixed(2) }, // Format to 2 decimal places
      });
    }
    let formattedValue = value.toFixed(2);
    console.log('Subtotal blur event triggered', e.target.value = formattedValue)
  }, [onChange]);

  const handleNextInvoiceNumber = useCallback(() => {
    const nextNumber = StorageService.incrementInvoiceNumber(formData.invoiceNumber);
    const event = {
      target: { name: 'invoiceNumber', value: nextNumber }
    } as React.ChangeEvent<HTMLInputElement>;
    onChange(event);
  }, [formData.invoiceNumber, onChange]);

  const isAutoGenerated = StorageService.isAutoGeneratedInvoiceNumber(formData.invoiceNumber);

  return (
    <form className="invoice-form" noValidate>
      <h1>Invoice Generator</h1>

      {/* Client Info */}
      <section className="form-section" aria-labelledby="client-info-title">
        <ClientInfoSection
          formData={formData}
          onChange={onChange}
          onBlur={handleBlur}
          errors={errors}
          touched={touched}
          onClientSelect={onClientSelect}
          validationTriggered={validationTriggered}
        />
      </section>

      {/* Invoice Details */}
      <section className="form-section" aria-labelledby="invoice-details-title">
        <h3 id="invoice-details-title" className="form-section-title">Invoice Details</h3>
        <FormRow>
          <div className="form-group">
            <div className="invoice-number-container">
              <FormInput
                label="Invoice Number"
                name="invoiceNumber"
                value={formData.invoiceNumber}
                onChange={onChange}
                onBlur={handleBlur}
                error={touched.invoiceNumber ? errors.invoiceNumber : undefined}
                placeholder="B-0"
                required
              />
              <button
                type="button"
                className="next-invoice-btn"
                onClick={handleNextInvoiceNumber}
                title="Get next auto-generated invoice number"
              >
                Next
              </button>
            </div>
            {!isAutoGenerated && (
              <small className="invoice-number-note">
                Manual invoice number - will be preserved after PDF generation
              </small>
            )}
          </div>

          <FormInput
            label="Invoice Date"
            name="invoiceDate"
            type="date"
            value={formData.invoiceDate}
            onChange={onChange}
            onBlur={handleBlur}
            error={touched.invoiceDate ? errors.invoiceDate : undefined}
            required
          />
        </FormRow>

        <div className="form-group">
          <FormTextArea
            label="Description"
            name="description"
            value={formData.description}
            onChange={onChange}
            onBlur={handleBlur}
            error={touched.description ? errors.description : undefined}
            placeholder="Detailed description of services provided"
            rows={8}
            required
          />
        </div>

        <FormRow>
          <FormInput
            label="Event Date"
            name="eventDate"
            type="date"
            value={formData.eventDate}
            onChange={onChange}
            onBlur={handleBlur}
            error={touched.eventDate ? errors.eventDate : undefined}
            required
          />

          <FormInput
            label="Venue"
            name="venue"
            value={formData.venue}
            onChange={onChange}
            onBlur={handleBlur}
            error={touched.venue ? errors.venue : undefined}
            placeholder="Event location"
            required
          />
        </FormRow>

        <FormRow>
          <FormInput
            label="Subtotal ($)"
            name="subtotal"
            type="number"
            value={formData.subtotal}
            onChange={onChange} // Use default change handler
            onBlur={handleSubtotalBlur} // Format value on blur
            onFocus={handleSubtotalFocus} // Select all text on focus
            placeholder="0.00"
            required
          />

          <FormInput
            label="GST Rate (%)"
            name="gstRate"
            type="number"
            value={formData.gstRate}
            onChange={onChange}
            onBlur={handleBlur}
            error={touched.gstRate ? errors.gstRate : undefined}
            placeholder="0"
            required
            min="0"
            max="100"
            step="1"
          />
        </FormRow>

        <div className="form-group">
          <FormLabel htmlFor="paymentTerms" required>Payment Terms</FormLabel>
          <select
            id="paymentTerms"
            name="paymentTerms"
            value={formData.paymentTerms}
            onChange={onChange}
            onBlur={handleBlur}
            className="form-select"
            required
          >

            <option value="7">Net 7 Days</option>
            <option value="14">Net 14 Days</option>
            <option value="30">Net 30 Days</option>
            <option value="0">Due Immediately</option>
          </select>
        </div>
      </section>
    </form>
  );
};
