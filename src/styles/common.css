:root {
  --primary-color: #6a0dad;
  --primary-light: #9b59b6;
  --primary-dark: #4a148c;
  --secondary-color: #cccccc;
  --secondary-light: #e6e6e6;
  --secondary-dark: #999999;
  --white: #ffffff;
  --text-dark: #333333;
  --text-light: #666666;
  --a4-width: 210mm;
  --a4-height: 297mm;
}

/* Shared styles for buttons */
.button {
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
}

.button:hover:not(:disabled) {
  background-color: var(--primary-dark);
}

.button:disabled {
  background-color: var(--secondary-dark);
  cursor: not-allowed;
}

/* Shared styles for form inputs */
.form-input,
.form-textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--secondary-color);
  border-radius: 4px;
  font-size: 1rem;
  box-sizing: border-box;
}

.form-input-error,
/* .form-textarea-error {
  border-color: var(--primary-dark);
} */
.error-message {
  color: var(--primary-dark);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}
