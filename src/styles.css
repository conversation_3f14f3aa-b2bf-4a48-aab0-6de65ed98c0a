:root {
  --primary-color: #6a0dad; /* Purple */
  --primary-light: #9b59b6; /* Lighter purple */
  --primary-dark: #4a148c; /* Darker purple */
  --secondary-color: #cccccc; /* Light gray */
  --secondary-light: #e6e6e6; /* Lighter gray */
  --secondary-dark: #999999; /* Darker gray */
  --white: #ffffff;
  --text-dark: #333333;
  --text-light: #666666;
  --border-radius: 4px;
  --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
  --error-color: #e53e3e;
  --error-bg: #ffe5e5;
  /* A4 dimensions in pixels (assuming 96 DPI) */
  --a4-width: 794px;  /* 210mm at 96 DPI */
  --a4-height: 1123px; /* 297mm at 96 DPI */
}

/* Base layout */
body {
  font-family: 'Roboto', 'Segoe UI', Aria<PERSON>, sans-serif;
  margin: 0;
  padding: 0;
  color: var(--text-dark);
  background-color: var(--secondary-light);
  text-rendering: optimizeSpeed;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  will-change: transform; /* Optimize for animations */
}

.app, .invoice-container {
  display: flex;
  min-height: 100vh;
  flex-direction: row;
}

@media (max-width: 768px) {
  .app, .invoice-container {
    flex-direction: column;
  }
}

.form, .form-hidden {
  display: none;
}
h3 {
    width: 100%;
    position: relative;
    padding-bottom: 10px;
    margin-bottom: 20px;
    display: block;
}

h3:after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary-color);
}
.form, .form-section {
  /*width: 50%;*/
  padding: 2rem;
  background: var(--white);
  overflow-y: auto;
  box-shadow: var(--box-shadow);
}

.preview, .preview-section {
  width: 50%;
  padding: 2rem;
  background: var(--secondary-light);
  display: flex;
  flex-direction: column;
  align-items: center;
}

@media (max-width: 768px) {
  .form, .form-section, .preview, .preview-section {
    width: 100%;
  }
}

/* Form elements */
input, textarea, select {
  display: block;
  width: 100%;
  padding: 0.75rem;
  margin-bottom: 1rem;
  border: 1px solid var(--secondary-color);
  border-radius: var(--border-radius);
  font-size: 0.95rem;
  transition: var(--transition);
  box-sizing: border-box;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(106, 13, 173, 0.2);
}

textarea {
  resize: vertical;
  min-height: 100px;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--primary-dark);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

h1, h2, h3 {
  color: var(--primary-dark);
}

/* Tables */
.items-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1.5rem;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.items-table th, .items-table td {
  padding: 0.75rem;
  text-align: left;
}

.items-table th {
  background-color: var(--primary-color);
  color: var(--white);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.85rem;
}

.items-table tr:nth-child(even) {
  background-color: var(--secondary-light);
}

.items-table tr:hover {
  background-color: rgba(106, 13, 173, 0.05);
}

.items-table input {
  margin-bottom: 0;
  width: 100%;
  border: 1px solid transparent;
  background-color: transparent;
  padding: 0.5rem;
}

.items-table input:focus {
  background-color: var(--white);
  border: 1px solid var(--primary-light);
}

/* Buttons */
button {
  padding: 0.75rem 1.5rem;
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

button:active {
  transform: translateY(0);
}

button:disabled {
  background-color: var(--secondary-color);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.download-btn {
  margin-top: 2rem;
  display: inline-block;
  text-decoration: none;
  text-align: center;
  width: 100%;
  max-width: 300px;
}

.download-btn a {
  text-decoration: none !important;
  display: block;
  width: 100%;
  color: inherit !important;
}

.download-pdf-btn {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  background-color: var(--primary-color);
  color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  width: 100%;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;

  cursor: pointer;
}

.download-pdf-btn:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.download-pdf-btn:disabled {
  background-color: var(--secondary-color);
  transform: none;
  box-shadow: none;
  cursor: not-allowed;
}

.error-btn {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  background-color: #e53e3e;
  color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  width: 100%;
  margin-bottom: 2rem;
  cursor: pointer;
}

/* Preview styles */
.preview-actions {
  margin-bottom: 2rem;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.preview-actions h2 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
  font-weight: 700;
}

.invoice-preview {

  padding: 2rem;
  margin: 2rem 0;
  border: 1px solid var(--secondary-color);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: var(--a4-width);
  min-height: var(--a4-height);
  max-width: 100%;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
  background-image: url('/src/assets/images/BelindaMarks-Hero-img.png');
  background-size: cover;
  background-position: top center;
  background-repeat: no-repeat;
  background-position: center top; 
  position: relative;
}

.invoice-preview::before {
  /* .header-bg-image::before { */
 content: "";
 position: absolute;
 top: 0;
 left: 0;
 width: 100%;
 height: 244px;
 background-image: url('/src/assets/images/BelindaMarks-Hero-img.png');
 background-repeat: no-repeat;
 background-size: cover; /* Ensures image covers entire container */
 background-position: center top;
 background-position-y: -50px; /* Adjust this value to move image up/down */
 opacity: 0.5; /* 50% opacity */
 
 /* Optional clipping path */
 clip-path: polygon(
     0% 0%,     /* Top left */
     100% 0%,   /* Top right */
     100% 90%,  /* Bottom right */
     0% 90%     /* Bottom left */
 );
 z-index: -1; /* Ensures background is behind content */
}
 
@media (max-width: 480px) {
  .invoice-branding h2,  h1.invoice-title {
    font-size: 1.1rem;
  }

  .invoice-preview::before {
    height: 200px;
  }
}
.invoice-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border-bottom: 2px solid var(--primary-color);
}

.invoice-branding {
  display: flex;
  flex-direction: column;
}

.invoice-branding h2 {
  margin: 0 0 0.5rem 0;
}

.invoice-branding p {
  margin: 0;
  line-height: 1.4;
}

.invoice-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.invoice-number, .thank-you {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--primary-dark);
  margin-top: 0.5rem;
}

.invoice-meta, .invoice-details {
  margin-bottom: 2rem;
}
.invoice-details-section, .payment-details{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 2rem; /* Add spacing between columns */
}

.invoice-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2rem;
}

.invoice-details-left {
  flex: 1;
  text-align: left;
}

.invoice-details-right {
  flex: 1;
  text-align: right;
}

.invoice-meta-column, .invoice-details-column {
  flex: 1;
}

.invoice-meta-column:last-child, .invoice-details-column:last-child {
  text-align: right;
}

.client-info, .invoice-info {
  margin-top: 1rem;
}

.client-info {
  text-align: left;
}

.invoice-info {
  text-align: right;
}

.client-info-row {
  display: flex;
  margin-bottom: 0.5rem;
  align-items: flex-start;
}

.client-info-label {
  width: 120px;
  font-weight: 600;
  /* color: var(--primary-color); */
  text-align: left;
}

.client-info-value {
  flex: 1;
  text-align: left;
}

/* Update heading styles */
.invoice-details h3 {
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--primary-color);
  color: var(--primary-dark);
}

/* Ensure consistent spacing in the right column */
.invoice-details-right p {
  margin: 0 0 0.5rem 0;
}

.invoice-details-right p:last-child {
  margin-bottom: 0;
}

.invoice-label, .attention-label, .client-label {
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 0.25rem;
  display: block;
}

.invoice-value {
  margin-bottom: 1rem;
}

.invoice-description {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: var(--secondary-light);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--primary-color);
}
.event-details-container {
  display: grid;
  grid-template-columns: 40% 60%;
  width: 100%;
  border-left: 4px solid var(--primary-color);
  min-height: 200px; 
  background-color: var(--secondary-light);
}

.event-details-left {
  display: flex;
  flex-direction: column;
  justify-content: center; /* Vertically center the content */
  padding: 1.5rem;
  margin-bottom: 1rem; /* Add margin bottom between venue-row */
}
.event-details-left .venue-row {
  margin-bottom: 1rem; /* Add margin bottom between venue-row */
}
.venue-label {
  font-weight: bold;
}
.event-details-right {
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
  border-left: none; /* Remove left border for the right column */
}

.description-text {
  margin-bottom: 1rem;
}

.description-text p {
  margin: 0 0 0.5rem 0;
  line-height: 1.5;
  white-space: pre-line;
}

.description-details {
  margin-top: 1.5rem;
  display: flex;
  justify-content: space-between;
  padding-top: 1rem;
  border-top: 1px dashed var(--secondary-color);
}

.invoice-totals {
  margin-top: 2rem;
  border-top: 1px solid var(--secondary-color);
  padding-top: 1.5rem;
}

.total-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
  padding: 0.5rem 1rem;
}

.total-row:nth-child(even) {
  background-color: var(--secondary-light);
  border-radius: var(--border-radius);
}

.grand-total {
  font-weight: 700;
  font-size: 1.2rem;
  margin-top: 1rem;
  padding: 1rem;
  border-top: 2px solid var(--primary-color);
  background-color: rgba(106, 13, 173, 0.05);
  border-radius: var(--border-radius);
  color: var(--primary-dark);
}

.invoice-footer, .thank-you-section {
  margin-top: 3rem;
  text-align: center;
  font-style: italic;
  color: var(--text-light);
  padding-top: 1.5rem;
  border-top: 1px dashed var(--secondary-color);
}

/* Form section title */
.form-section-title {
  width: 100%;
  position: relative;
  padding-bottom: 10px;
  margin-bottom: 20px;
  display: block;
  color: var(--primary-dark);
}

.form-section-title:after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2px;
  background-color: var(--primary-color);
}

/* Additional utility classes */
.text-primary {
  color: var(--primary-color);
}

.text-center {
  text-align: center;
}

.mt-2 {
  margin-top: 2rem;
}

.mb-2 {
  margin-bottom: 2rem;
}

.form-group.has-error .form-input {
  border-color: var(--error-color);
  background-color: var(--error-bg);
}

.form-error-message {
  color: var(--error-color);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.validation-summary {
  color: var(--error-color);
  background-color: var(--error-bg);
  padding: 1rem;
  border-radius: var(--border-radius);
  margin: 1rem 0;
}

.validation-summary h4 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

.validation-summary ul {
  margin: 0;
  padding-left: 1.5rem;
}

.validation-summary li {
  margin-bottom: 0.25rem;
}

.generate-checkbox {
  margin: 1rem 0;
  padding: 1rem;
  background-color: var(--secondary-light);
  border-radius: var(--border-radius);
  text-align: center;
}

.generate-checkbox label {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
}

.generate-checkbox input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.required-mark {
  color: var(--error-color);
  margin-left: 0.25rem;
}

.invoice-container {
  display: grid;
  grid-template-columns: minmax(0, 1fr) minmax(0, 1fr); /* Changed from 1fr 1fr */
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

.form-section {
  background: var(--white);
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.preview-section {
  position: sticky;
  top: 2rem;
  background: var(--white);
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  max-height: calc(100vh - 4rem);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%; /* Added to ensure full width */
}

.invoice-preview {
  background: white;
  padding: 2rem;
  margin: 2rem 0;
  border: 1px solid var(--secondary-color);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%; /* Changed from var(--a4-width) */
  max-width: 100%;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  flex-direction: column;
  height: auto !important;
  min-height: fit-content;
  overflow: visible;
}

.generate-checkbox {
  margin: 1rem 0;
  padding: 1rem;
  background-color: var(--secondary-light);
  border-radius: var(--border-radius);
}

.generate-checkbox label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.generate-checkbox input[type="checkbox"]:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.generate-checkbox .form-error-message {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: var(--error-color);
}

@media (max-width: 1024px) {
  .invoice-container {
    grid-template-columns: 1fr;
  }

  .preview-section {
    position: static;
    max-height: none;
  }

  .invoice-preview {
    width: 100%;
  }
}

/* Mobile styles */
@media (max-width: 768px) {
  .invoice-container {
    grid-template-columns: 1fr;
    padding: 1rem;
  }

  .preview-section {
    position: static;
    max-height: none;
    padding: 1rem;
  }

  .invoice-preview {
    width: 100%;
    min-height: fit-content;
    height: auto !important;
    padding: 1rem;
    margin: 1rem 0;
    overflow: visible;
  }
}

/* Ensure preview content scales properly */
.invoice-preview {
  transform-origin: top center;
  scale: 1;
  height: auto !important;
  min-height: fit-content;
  overflow: visible;
}

@media (max-width: 840px) {
  .invoice-preview {
    scale: '1';
    height: auto !important;
    min-height: fit-content;
    overflow: visible;
  }
}

@media (max-width: 768px) {
  .invoice-preview {
    scale: 1;
    transform: none;
    height: auto !important;
    min-height: fit-content;
    overflow: visible;
  }
}

/* Loading spinner */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100%;
  background-color: var(--secondary-light);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid var(--secondary-color);
  border-top: 5px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Add styles for better preview readability */
.invoice-preview .invoice-header,
.invoice-preview .invoice-details,
.invoice-preview .invoice-description,
.invoice-preview .invoice-totals {
  margin-bottom: 2rem;
}

.invoice-preview h1,
.invoice-preview h2,
.invoice-preview h3 {
  margin-bottom: 1rem;
}

.invoice-preview p {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.tools-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--secondary-light);
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
  gap: 1.5rem; /* Added gap for spacing */
}

.tools-left {
  display: flex;
  align-items: center;

  gap: 1rem;
}

.tools-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.zoom-panel {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem;
  border-radius: var(--border-radius);

}

.zoom-button {
  width: 24px;
  height: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--secondary-color);
  border-radius: 3px;
  background: var(--primary-dark);
  cursor: pointer;
  font-size: 12px;
  transition: var(--transition);
  margin-bottom: 0;
}

.zoom-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.zoom-value {
  font-size: 0.75rem;
  padding: 0.25rem 0;
}

.generate-checkbox {
  margin: 0;
  padding: 0.5rem 1rem;
  background-color: transparent;
}

/* Form Styling Enhancements */
.form-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.form-section-title {
  color: var(--primary-dark);
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--primary-light);
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  color: var(--text-dark);
  font-weight: 500;
}

.required-indicator {
  color: var(--error-color);
  margin-left: 4px;
}

.helper-text {
  font-size: 0.875rem;
  color: var(--text-light);
  margin-top: 0.25rem;
}

.form-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--secondary-color);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.form-input:focus {
  border-color: var(--primary-light);
  box-shadow: 0 0 0 2px rgba(106, 13, 173, 0.1);
  outline: none;
}

.form-input.error {
  border-color: var(--error-color);
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

/* Accessibility focus indicators */
:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

@media (max-width: 480px) {
  .invoice-preview {
    scale: 1;
    height: auto !important;
    min-height: fit-content;
    overflow: visible;
  }
  .form, .form-section, .preview, .preview-section {
    padding: 0.2rem;
    font-size: 0.9rem;
    width: 100%;
  }
  #client-contact-row {
    display: flex;
    flex-direction: column; /* Stack items vertically */
    gap: 1rem; /* Add spacing between items */
  }
  .invoice-preview {
    padding: 0.5rem;
    margin: 0.5rem 0;
    font-size: 0.85rem;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  .button {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .invoice-container {
    grid-template-columns: 1fr; /* Reflow to a single column */
    padding: 1rem;
  }

  .form-group {
    width: 100%; /* Reduce input field widths */
  }

  .invoice-details-section, .payment-details {
    flex-direction: column; /* Stack columns vertically */
    gap: 1rem; /* Add spacing between stacked items */
  }

  .invoice-details-left, .invoice-details-right {
    text-align: left; /* Align text to the left for better readability */
  }
}

/* Invoice number container and Next button */
.invoice-number-container {
  display: flex;
  gap: 0.5rem;
  align-items: flex-end;
  flex-wrap: nowrap;
}

.invoice-number-container .form-input {
  flex: 1;
  margin-bottom: 0;
  min-width: 0;
}

.next-invoice-btn {
  padding: 0.75rem 1rem;
  background-color: var(--primary-light);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.85rem;
  white-space: nowrap;
  height: fit-content;
  flex-shrink: 0;
}

.next-invoice-btn:hover {
  background-color: var(--primary-color);
  transform: translateY(-1px);
}

.next-invoice-btn:active {
  transform: translateY(0);
}

.invoice-number-note {
  display: block;
  margin-top: 0.25rem;
  font-size: 0.8rem;
  color: var(--text-light);
  font-style: italic;
}

@media (max-width: 768px) {
  .invoice-number-container {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .next-invoice-btn {
    align-self: flex-start;
  }
}

/* Confirmation Dialog */
.confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.confirmation-dialog {
  background: white;
  border-radius: 8px;
  padding: 24px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.confirmation-dialog h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.confirmation-dialog p {
  margin: 0 0 24px 0;
  color: #666;
  line-height: 1.5;
}

.confirmation-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.confirmation-actions .btn {
  min-width: 80px;
}
