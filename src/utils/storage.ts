import { Client, Invoice } from '../types/invoice';

const CLIENTS_STORAGE_KEY = 'invoice_generator_clients';
const INVOICES_STORAGE_KEY = 'invoice_generator_invoices';
const LAST_INVOICE_NUMBER_KEY = 'invoice_generator_last_invoice_number';

export class StorageService {
  // Client Management
  static getClients(): Client[] {
    try {
      const clients = localStorage.getItem(CLIENTS_STORAGE_KEY);
      return clients ? JSON.parse(clients) : [];
    } catch (error) {
      console.error('Error loading clients:', error);
      return [];
    }
  }

  static saveClients(clients: Client[]): void {
    try {
      localStorage.setItem(CLIENTS_STORAGE_KEY, JSON.stringify(clients));
    } catch (error) {
      console.error('Error saving clients:', error);
    }
  }

  static addClient(client: Omit<Client, 'id' | 'createdAt' | 'lastUsed' | 'invoiceCount'>): Client {
    const clients = this.getClients();
    const newClient: Client = {
      ...client,
      id: this.generateId(),
      createdAt: new Date().toISOString(),
      lastUsed: new Date().toISOString(),
      invoiceCount: 0
    };
    
    clients.push(newClient);
    this.saveClients(clients);
    return newClient;
  }

  static updateClient(clientId: string, updates: Partial<Client>): Client | null {
    const clients = this.getClients();
    const index = clients.findIndex(c => c.id === clientId);
    
    if (index === -1) return null;
    
    clients[index] = { ...clients[index], ...updates };
    this.saveClients(clients);
    return clients[index];
  }

  static getClient(clientId: string): Client | null {
    const clients = this.getClients();
    return clients.find(c => c.id === clientId) || null;
  }

  static searchClients(query: string): Client[] {
    const clients = this.getClients();
    const lowerQuery = query.toLowerCase();
    
    return clients.filter(client => 
      client.name.toLowerCase().includes(lowerQuery) ||
      client.email.toLowerCase().includes(lowerQuery) ||
      client.phone.includes(query)
    );
  }

  // Invoice Management
  static getInvoices(): Invoice[] {
    try {
      const invoices = localStorage.getItem(INVOICES_STORAGE_KEY);
      return invoices ? JSON.parse(invoices) : [];
    } catch (error) {
      console.error('Error loading invoices:', error);
      return [];
    }
  }

  static saveInvoices(invoices: Invoice[]): void {
    try {
      localStorage.setItem(INVOICES_STORAGE_KEY, JSON.stringify(invoices));
    } catch (error) {
      console.error('Error saving invoices:', error);
    }
  }

  static addInvoice(invoice: Omit<Invoice, 'id' | 'createdAt' | 'updatedAt'>): Invoice {
    const invoices = this.getInvoices();
    const newInvoice: Invoice = {
      ...invoice,
      id: this.generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    invoices.push(newInvoice);
    this.saveInvoices(invoices);
    
    // Update client's invoice count and last used date
    this.updateClientInvoiceCount(invoice.clientId);
    
    return newInvoice;
  }

  static getInvoicesByClient(clientId: string): Invoice[] {
    const invoices = this.getInvoices();
    return invoices.filter(invoice => invoice.clientId === clientId);
  }

  static getInvoice(invoiceId: string): Invoice | null {
    const invoices = this.getInvoices();
    return invoices.find(invoice => invoice.id === invoiceId) || null;
  }

  static getInvoiceByNumber(invoiceNumber: string): Invoice | null {
    const invoices = this.getInvoices();
    return invoices.find(invoice => invoice.invoiceNumber === invoiceNumber) || null;
  }

  // Invoice Number Management
  static getLastInvoiceNumber(): string {
    try {
      return localStorage.getItem(LAST_INVOICE_NUMBER_KEY) || 'B-000';
    } catch (error) {
      console.error('Error loading last invoice number:', error);
      return 'B-000';
    }
  }

  static saveLastInvoiceNumber(invoiceNumber: string): void {
    try {
      const currentLast = this.getLastInvoiceNumber();
      
      // Only update if the new number is higher than the current one
      // This prevents manual entries from interfering with auto-generation
      const currentMatch = currentLast.match(/B-(\d+)/);
      const newMatch = invoiceNumber.match(/B-(\d+)/);
      
      if (currentMatch && newMatch) {
        const currentNum = parseInt(currentMatch[1]);
        const newNum = parseInt(newMatch[1]);
        
        if (newNum > currentNum) {
          localStorage.setItem(LAST_INVOICE_NUMBER_KEY, invoiceNumber);
        }
      } else {
        // If either number doesn't match the pattern, save the new one
        localStorage.setItem(LAST_INVOICE_NUMBER_KEY, invoiceNumber);
      }
    } catch (error) {
      console.error('Error saving last invoice number:', error);
    }
  }

  static generateNextInvoiceNumber(): string {
    const lastNumber = this.getLastInvoiceNumber();
    const match = lastNumber.match(/B-(\d+)/);
    
    if (match) {
      const nextNum = parseInt(match[1]) + 1;
      return `B-${nextNum.toString().padStart(3, '0')}`;
    }
    
    return 'B-';
  }

  static incrementInvoiceNumber(currentNumber: string): string {
    // Extract prefix and number from current invoice number
    // Pattern: any letters/dashes followed by numbers
    const match = currentNumber.match(/^([A-Za-z-]+)(\d+)$/);
    
    if (match) {
      const prefix = match[1];
      const currentNum = parseInt(match[2]);
      const nextNum = currentNum + 1;
      
      // Preserve the original number of digits in the number part
      const numDigits = match[2].length;
      const nextNumStr = nextNum.toString().padStart(numDigits, '0');
      
      return `${prefix}${nextNumStr}`;
    }
    
    // If no pattern match, return the original number
    return currentNumber;
  }

  static isAutoGeneratedInvoiceNumber(invoiceNumber: string): boolean {
    // Check if the invoice number follows the auto-generation pattern B-XXX
    return /^B-\d{3}$/.test(invoiceNumber);
  }

  static getInvoiceNumberValue(invoiceNumber: string): number {
    const match = invoiceNumber.match(/B-(\d+)/);
    return match ? parseInt(match[1]) : 0;
  }

  // Helper methods
  private static generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private static updateClientInvoiceCount(clientId: string): void {
    const client = this.getClient(clientId);
    if (client) {
      this.updateClient(clientId, {
        invoiceCount: client.invoiceCount + 1,
        lastUsed: new Date().toISOString()
      });
    }
  }

  // Data cleanup and validation
  static cleanupDuplicateClients(): { removed: number; merged: number } {
    const clients = this.getClients();
    const uniqueClients = new Map<string, Client>();
    let removed = 0;
    let merged = 0;

    clients.forEach(client => {
      // Create a key based on name and email for comparison
      const key = `${client.name.toLowerCase()}-${client.email.toLowerCase()}`;
      
      if (uniqueClients.has(key)) {
        // This is a duplicate, merge the data
        const existing = uniqueClients.get(key)!;
        existing.invoiceCount += client.invoiceCount;
        
        // Keep the most recent lastUsed date
        if (new Date(client.lastUsed) > new Date(existing.lastUsed)) {
          existing.lastUsed = client.lastUsed;
        }
        
        // Merge attention field if one is empty
        if (!existing.attention && client.attention) {
          existing.attention = client.attention;
        }
        
        // Keep the phone number from the most recently used client
        if (new Date(client.lastUsed) > new Date(existing.lastUsed)) {
          existing.phone = client.phone;
        }
        
        removed++;
        merged++;
      } else {
        // This is a new unique client
        uniqueClients.set(key, { ...client });
      }
    });

    // Convert map back to array
    const cleanedClients = Array.from(uniqueClients.values());
    
    // Save the cleaned clients
    this.saveClients(cleanedClients);
    
    console.log(`Cleanup completed: ${removed} duplicate clients removed, ${merged} clients merged`);
    
    return { removed, merged };
  }

  static cleanupDuplicateInvoices(): { removed: number; kept: number } {
    const invoices = this.getInvoices();
    const uniqueInvoices = new Map<string, Invoice>();
    let removed = 0;
    let kept = 0;

    // Sort invoices by creation date (newest first) to keep the most recent
    const sortedInvoices = invoices.sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    sortedInvoices.forEach(invoice => {
      if (uniqueInvoices.has(invoice.invoiceNumber)) {
        // This is a duplicate invoice number, skip it (keep the first one we encountered, which is the newest)
        removed++;
        console.log(`Removing duplicate invoice: ${invoice.invoiceNumber} (ID: ${invoice.id})`);
      } else {
        // This is a new unique invoice number
        uniqueInvoices.set(invoice.invoiceNumber, invoice);
        kept++;
      }
    });

    // Convert map back to array
    const cleanedInvoices = Array.from(uniqueInvoices.values());
    
    // Save the cleaned invoices
    this.saveInvoices(cleanedInvoices);
    
    console.log(`Invoice cleanup completed: ${removed} duplicate invoices removed, ${kept} invoices kept`);
    
    return { removed, kept };
  }

  static cleanupAllDuplicates(): { clients: { removed: number; merged: number }; invoices: { removed: number; kept: number } } {
    const clientResult = this.cleanupDuplicateClients();
    const invoiceResult = this.cleanupDuplicateInvoices();
    
    return {
      clients: clientResult,
      invoices: invoiceResult
    };
  }

  static getDuplicateCounts(): { clientDuplicates: number; invoiceDuplicates: number } {
    const clients = this.getClients();
    const invoices = this.getInvoices();
    
    // Count client duplicates
    const clientMap = new Map<string, number>();
    clients.forEach(client => {
      const key = `${client.name.toLowerCase()}-${client.email.toLowerCase()}`;
      clientMap.set(key, (clientMap.get(key) || 0) + 1);
    });
    const clientDuplicates = Array.from(clientMap.values()).filter(count => count > 1).length;
    
    // Count invoice duplicates
    const invoiceMap = new Map<string, number>();
    invoices.forEach(invoice => {
      invoiceMap.set(invoice.invoiceNumber, (invoiceMap.get(invoice.invoiceNumber) || 0) + 1);
    });
    const invoiceDuplicates = Array.from(invoiceMap.values()).filter(count => count > 1).length;
    
    return { clientDuplicates, invoiceDuplicates };
  }
} 