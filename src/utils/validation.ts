import { FormData, ValidationError } from '../types/invoice';

/**
 * Validates a single field in the form.
 * @param field - The name of the field to validate.
 * @param value - The value of the field to validate.
 * @returns A ValidationError object if validation fails, otherwise undefined.
 */
export const validateField = (
  field: keyof FormData,
  value: any
): ValidationError | undefined => {
  switch (field) {
    case 'clientName':
    case 'clientEmail':
    case 'description':
    case 'venue':
      if (!value || value.trim() === '') {
        return { message: `${field} is required`, type: 'required' };
      }
      break;
    case 'eventDate':
      if (!value) {
        return { message: 'Event date is required', type: 'required' };
      }
      break;
    case 'subtotal':
      if (!value || value <= 0) {
        return { message: 'Subtotal must be greater than 0', type: 'required' };
      }
      break;
    case 'clientEmail':
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        return { message: 'Invalid email format', type: 'format' };
      }
      break;
    default:
      break;
  }
  return undefined;
};

/**
 * Validates the entire form.
 * @param formData - The form data to validate.
 * @returns An object containing validation errors for each field.
 */
export const validateForm = (
  formData: FormData
): Partial<Record<keyof FormData, ValidationError>> => {
  const errors: Partial<Record<keyof FormData, ValidationError>> = {};

  Object.keys(formData).forEach((key) => {
    const field = key as keyof FormData;
    const error = validateField(field, formData[field]);
    if (error) {
      errors[field] = error;
    }
  });

  return errors;
};
