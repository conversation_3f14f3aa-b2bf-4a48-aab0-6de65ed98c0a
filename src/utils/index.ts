export const validateEmail = (email: string): boolean => {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
};

export const calculateDueDate = (invoiceDate: string, paymentTerms: number): string => {
  const date = new Date(invoiceDate);
  date.setDate(date.getDate() + paymentTerms);
  return date.toISOString().split('T')[0];
};

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-AU', {
    style: 'currency',
    currency: 'AUD'
  }).format(amount);
};

export const validateField = (field: string, value: any): boolean => {
  // Add validation logic for individual fields
  return value !== undefined && value !== null && value !== '';
};

export const validateForm = (formData: Record<string, any>): boolean => {
  // Add validation logic for the entire form
  return Object.values(formData).every(value => validateField('', value));
};

// Export storage service
export { StorageService } from './storage';